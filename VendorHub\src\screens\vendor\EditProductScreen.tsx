import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles, useProducts } from '../../hooks';
import { Card, Button, Input, LoadingSpinner, ImagePicker } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../../constants/theme';
import { PRODUCT_CATEGORIES } from '../../constants';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { ProductCategory, Product } from '../../contexts/DataContext';

interface EditProductScreenProps {
  navigation: any;
  route: {
    params: {
      productId: string;
    };
  };
}

interface ProductFormData {
  name: string;
  description: string;
  price: string;
  originalPrice: string;
  category: ProductCategory | '';
  inventory: string;
  tags: string;
  isActive: boolean;
  images: string[];
}

export const EditProductScreen: React.FC<EditProductScreenProps> = ({ navigation, route }) => {
  const { productId } = route.params;
  const styles = useThemedStyles(createStyles);
  const { getProductById, updateProduct } = useProducts();
  
  const [product, setProduct] = useState<Product | null>(null);
  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: '',
    price: '',
    originalPrice: '',
    category: '',
    inventory: '',
    tags: '',
    isActive: true,
    images: [],
  });

  const [errors, setErrors] = useState<Partial<ProductFormData>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showCategoryPicker, setShowCategoryPicker] = useState(false);

  useEffect(() => {
    const productData = getProductById(productId);
    if (productData) {
      setProduct(productData);
      setFormData({
        name: productData.name,
        description: productData.description,
        price: productData.price.toString(),
        originalPrice: productData.originalPrice?.toString() || '',
        category: productData.category,
        inventory: productData.inventory.toString(),
        tags: productData.tags.join(', '),
        isActive: productData.isActive,
        images: productData.images || [],
      });
    } else {
      Alert.alert('Error', 'Product not found', [
        { text: 'OK', onPress: () => navigation.goBack() }
      ]);
    }
  }, [productId, getProductById, navigation]);

  const validateForm = (): boolean => {
    const newErrors: Partial<ProductFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Product description is required';
    }

    if (!formData.price.trim()) {
      newErrors.price = 'Price is required';
    } else if (isNaN(Number(formData.price)) || Number(formData.price) <= 0) {
      newErrors.price = 'Please enter a valid price';
    }

    if (formData.originalPrice && (isNaN(Number(formData.originalPrice)) || Number(formData.originalPrice) <= 0)) {
      newErrors.originalPrice = 'Please enter a valid original price';
    }

    if (!formData.category) {
      newErrors.category = 'Please select a category';
    }

    if (!formData.inventory.trim()) {
      newErrors.inventory = 'Inventory quantity is required';
    } else if (isNaN(Number(formData.inventory)) || Number(formData.inventory) < 0) {
      newErrors.inventory = 'Please enter a valid inventory quantity';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !product) return;

    setIsLoading(true);
    try {
      const updatedData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        price: Number(formData.price),
        originalPrice: formData.originalPrice ? Number(formData.originalPrice) : undefined,
        category: formData.category as ProductCategory,
        inventory: Number(formData.inventory),
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
        isActive: formData.isActive,
        images: formData.images,
        updatedAt: new Date().toISOString(),
      };

      await updateProduct(productId, updatedData);
      
      Alert.alert(
        'Success',
        'Product updated successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to update product. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: keyof ProductFormData, value: string | boolean | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field as keyof typeof errors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleCategorySelect = (category: ProductCategory) => {
    setFormData(prev => ({ ...prev, category }));
    setShowCategoryPicker(false);
    if (errors.category) {
      setErrors(prev => ({ ...prev, category: undefined }));
    }
  };

  const handleToggleStatus = () => {
    const newStatus = !formData.isActive;
    setFormData(prev => ({ ...prev, isActive: newStatus }));
    
    Alert.alert(
      newStatus ? 'Activate Product' : 'Deactivate Product',
      `Are you sure you want to ${newStatus ? 'activate' : 'deactivate'} this product?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: newStatus ? 'Activate' : 'Deactivate',
          onPress: () => {
            // Status is already updated in formData
          },
        },
      ]
    );
  };

  const renderCategoryPicker = () => (
    <Card style={styles.categoryPicker} variant="elevated">
      <Text style={styles.categoryPickerTitle}>Select Category</Text>
      {Object.values(PRODUCT_CATEGORIES).map((category) => (
        <TouchableOpacity
          key={category}
          style={styles.categoryOption}
          onPress={() => handleCategorySelect(category)}
        >
          <Text style={styles.categoryOptionText}>{category}</Text>
          <Ionicons name="chevron-forward" size={20} color="#CCCCCC" />
        </TouchableOpacity>
      ))}
      <Button
        title="Cancel"
        onPress={() => setShowCategoryPicker(false)}
        variant="outline"
        style={styles.cancelButton}
      />
    </Card>
  );

  if (!product) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="large" />
          <Text style={styles.loadingText}>Loading product...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.title}>Edit Product</Text>
              <Text style={styles.subtitle}>
                Update your product information below
              </Text>
            </View>

            {/* Product Status */}
            <Card style={styles.section} variant="outlined">
              <View style={styles.statusSection}>
                <View style={styles.statusInfo}>
                  <Text style={styles.sectionTitle}>Product Status</Text>
                  <Text style={styles.statusDescription}>
                    {formData.isActive ? 'Product is currently active and visible to customers' : 'Product is inactive and hidden from customers'}
                  </Text>
                </View>
                <TouchableOpacity
                  style={[styles.statusToggle, formData.isActive && styles.statusToggleActive]}
                  onPress={handleToggleStatus}
                >
                  <View style={[styles.statusToggleThumb, formData.isActive && styles.statusToggleThumbActive]} />
                </TouchableOpacity>
              </View>
            </Card>

            {/* Basic Information */}
            <Card style={styles.section} variant="outlined">
              <Text style={styles.sectionTitle}>Basic Information</Text>
              
              <Input
                label="Product Name *"
                placeholder="Enter product name"
                value={formData.name}
                onChangeText={(value) => handleInputChange('name', value)}
                error={errors.name}
                style={styles.input}
              />

              <Input
                label="Description *"
                placeholder="Describe your product"
                value={formData.description}
                onChangeText={(value) => handleInputChange('description', value)}
                error={errors.description}
                multiline
                numberOfLines={4}
                style={styles.input}
              />

              <TouchableOpacity
                style={styles.categorySelector}
                onPress={() => setShowCategoryPicker(true)}
              >
                <Text style={styles.categoryLabel}>Category *</Text>
                <View style={styles.categoryValue}>
                  <Text style={[styles.categoryText, !formData.category && styles.placeholderText]}>
                    {formData.category || 'Select a category'}
                  </Text>
                  <Ionicons name="chevron-down" size={20} color="#CCCCCC" />
                </View>
                {errors.category && (
                  <Text style={styles.errorText}>{errors.category}</Text>
                )}
              </TouchableOpacity>
            </Card>

            {/* Pricing */}
            <Card style={styles.section} variant="outlined">
              <Text style={styles.sectionTitle}>Pricing</Text>
              
              <Input
                label="Price *"
                placeholder="0.00"
                value={formData.price}
                onChangeText={(value) => handleInputChange('price', value)}
                error={errors.price}
                keyboardType="numeric"
                leftIcon={<Text style={styles.currencySymbol}>BD</Text>}
                style={styles.input}
              />

              <Input
                label="Original Price (Optional)"
                placeholder="0.00"
                value={formData.originalPrice}
                onChangeText={(value) => handleInputChange('originalPrice', value)}
                error={errors.originalPrice}
                keyboardType="numeric"
                leftIcon={<Text style={styles.currencySymbol}>BD</Text>}
                style={styles.input}
              />
            </Card>

            {/* Inventory */}
            <Card style={styles.section} variant="outlined">
              <Text style={styles.sectionTitle}>Inventory</Text>
              
              <Input
                label="Quantity in Stock *"
                placeholder="0"
                value={formData.inventory}
                onChangeText={(value) => handleInputChange('inventory', value)}
                error={errors.inventory}
                keyboardType="numeric"
                style={styles.input}
              />
            </Card>

            {/* Product Images */}
            <Card style={styles.section} variant="outlined">
              <Text style={styles.sectionTitle}>Product Images</Text>

              <ImagePicker
                images={formData.images}
                onImagesChange={(images) => handleInputChange('images', images)}
                maxImages={5}
                title="Product Photos"
                subtitle="Update your product images"
              />

              <Text style={styles.helperText}>
                High-quality images help increase sales. The first image will be used as the main product image.
              </Text>
            </Card>

            {/* Additional Details */}
            <Card style={styles.section} variant="outlined">
              <Text style={styles.sectionTitle}>Additional Details</Text>
              
              <Input
                label="Tags (Optional)"
                placeholder="e.g., wireless, bluetooth, premium"
                value={formData.tags}
                onChangeText={(value) => handleInputChange('tags', value)}
                style={styles.input}
              />
            </Card>

            {/* Action Buttons */}
            <View style={styles.actionButtons}>
              <Button
                title="Cancel"
                onPress={() => navigation.goBack()}
                variant="outline"
                style={styles.cancelActionButton}
              />
              <Button
                title="Update Product"
                onPress={handleSubmit}
                loading={isLoading}
                style={styles.submitButton}
                leftIcon={<Ionicons name="checkmark-outline" size={20} color="#FFFFFF" />}
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Category Picker Modal */}
      {showCategoryPicker && (
        <View style={styles.modalOverlay}>
          {renderCategoryPicker()}
        </View>
      )}
    </SafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    loadingText: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      marginTop: SPACING.md,
    },
    keyboardAvoid: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      padding: SPACING.lg,
    },
    header: {
      marginBottom: SPACING.xl,
    },
    title: {
      fontSize: FONT_SIZES.xxl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    subtitle: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      lineHeight: 22,
    },
    section: {
      marginBottom: SPACING.lg,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
    },
    statusSection: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    statusInfo: {
      flex: 1,
      marginRight: SPACING.md,
    },
    statusDescription: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      lineHeight: 18,
    },
    statusToggle: {
      width: 50,
      height: 30,
      borderRadius: 15,
      backgroundColor: colors.border,
      justifyContent: 'center',
      padding: 2,
    },
    statusToggleActive: {
      backgroundColor: '#4CAF50',
    },
    statusToggleThumb: {
      width: 26,
      height: 26,
      borderRadius: 13,
      backgroundColor: '#FFFFFF',
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    statusToggleThumbActive: {
      transform: [{ translateX: 20 }],
    },
    input: {
      marginBottom: SPACING.md,
    },
    categorySelector: {
      marginBottom: SPACING.md,
    },
    categoryLabel: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.sm,
    },
    categoryValue: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      padding: SPACING.md,
      borderWidth: 1,
      borderColor: colors.border,
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: colors.surface,
    },
    categoryText: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
    },
    placeholderText: {
      color: colors.textSecondary,
    },
    errorText: {
      fontSize: FONT_SIZES.sm,
      color: '#FF6B6B',
      marginTop: SPACING.xs,
    },
    currencySymbol: {
      fontSize: FONT_SIZES.md,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    actionButtons: {
      flexDirection: 'row',
      gap: SPACING.md,
      marginTop: SPACING.xl,
      marginBottom: SPACING.xl,
    },
    cancelActionButton: {
      flex: 1,
    },
    submitButton: {
      flex: 2,
    },
    modalOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: SPACING.xl,
    },
    categoryPicker: {
      width: '100%',
      maxHeight: '80%',
    },
    categoryPickerTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
      textAlign: 'center',
    },
    categoryOption: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    categoryOptionText: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
    },
    cancelButton: {
      marginTop: SPACING.md,
    },
  });
