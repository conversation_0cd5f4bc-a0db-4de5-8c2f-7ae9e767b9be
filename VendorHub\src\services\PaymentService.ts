import { Alert } from 'react-native';
import { storage } from '../utils/storage';
import { CURRENCY } from '../constants';

export interface PaymentMethod {
  id: string;
  type: 'credit_card' | 'debit_card' | 'paypal' | 'apple_pay' | 'google_pay' | 'bank_transfer' | 'buy_now_pay_later';
  name: string;
  displayName: string;
  last4?: string;
  expiryMonth?: number;
  expiryYear?: number;
  brand?: string;
  isDefault: boolean;
  isEnabled: boolean;
  metadata?: {
    email?: string;
    bankName?: string;
    accountType?: string;
  };
}

export interface PaymentIntent {
  id: string;
  amount: number;
  currency: string;
  orderId: string;
  customerId: string;
  vendorId: string;
  paymentMethodId: string;
  status: 'pending' | 'processing' | 'succeeded' | 'failed' | 'cancelled';
  clientSecret?: string;
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentResult {
  success: boolean;
  paymentIntent?: PaymentIntent;
  error?: string;
  requiresAction?: boolean;
  actionUrl?: string;
}

export interface RefundRequest {
  paymentIntentId: string;
  amount?: number; // Partial refund if specified
  reason: string;
  metadata?: any;
}

export interface PaymentConfiguration {
  stripePublishableKey?: string;
  paypalClientId?: string;
  applePayMerchantId?: string;
  googlePayMerchantId?: string;
  enabledMethods: PaymentMethod['type'][];
  // Currency is always BHD - not configurable
  readonly currency: typeof CURRENCY.CODE;
  readonly country: typeof CURRENCY.COUNTRY;
}

class PaymentService {
  private static instance: PaymentService;
  private paymentMethods: Map<string, PaymentMethod> = new Map();
  private paymentIntents: Map<string, PaymentIntent> = new Map();
  private configuration: PaymentConfiguration = {
    enabledMethods: ['credit_card', 'debit_card', 'paypal'],
    currency: CURRENCY.CODE,
    country: CURRENCY.COUNTRY,
  };

  private constructor() {
    this.loadStoredData();
    this.initializePaymentProviders();
  }

  public static getInstance(): PaymentService {
    if (!PaymentService.instance) {
      PaymentService.instance = new PaymentService();
    }
    return PaymentService.instance;
  }

  // Configuration - Currency and country are not configurable (always BHD/BH)
  public updateConfiguration(config: Omit<Partial<PaymentConfiguration>, 'currency' | 'country'>) {
    this.configuration = {
      ...this.configuration,
      ...config,
      // Ensure currency and country remain BHD/BH
      currency: CURRENCY.CODE,
      country: CURRENCY.COUNTRY,
    };
    this.saveData();
  }

  public getConfiguration(): PaymentConfiguration {
    return { ...this.configuration };
  }

  // Payment Methods Management
  public async addPaymentMethod(
    userId: string,
    type: PaymentMethod['type'],
    data: any
  ): Promise<PaymentMethod> {
    const paymentMethod: PaymentMethod = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      type,
      name: this.generatePaymentMethodName(type, data),
      displayName: this.generateDisplayName(type, data),
      last4: data.last4,
      expiryMonth: data.expiryMonth,
      expiryYear: data.expiryYear,
      brand: data.brand,
      isDefault: this.paymentMethods.size === 0, // First method is default
      isEnabled: true,
      metadata: data.metadata,
    };

    this.paymentMethods.set(paymentMethod.id, paymentMethod);
    await this.saveData();

    return paymentMethod;
  }

  public async removePaymentMethod(paymentMethodId: string): Promise<void> {
    const paymentMethod = this.paymentMethods.get(paymentMethodId);
    if (!paymentMethod) {
      throw new Error('Payment method not found');
    }

    this.paymentMethods.delete(paymentMethodId);

    // If this was the default method, set another as default
    if (paymentMethod.isDefault && this.paymentMethods.size > 0) {
      const firstMethod = Array.from(this.paymentMethods.values())[0];
      firstMethod.isDefault = true;
      this.paymentMethods.set(firstMethod.id, firstMethod);
    }

    await this.saveData();
  }

  public async setDefaultPaymentMethod(paymentMethodId: string): Promise<void> {
    // Remove default from all methods
    for (const method of this.paymentMethods.values()) {
      method.isDefault = false;
    }

    // Set new default
    const paymentMethod = this.paymentMethods.get(paymentMethodId);
    if (paymentMethod) {
      paymentMethod.isDefault = true;
      this.paymentMethods.set(paymentMethodId, paymentMethod);
      await this.saveData();
    }
  }

  public getPaymentMethods(userId: string): PaymentMethod[] {
    return Array.from(this.paymentMethods.values())
      .filter(method => method.isEnabled)
      .sort((a, b) => {
        if (a.isDefault) return -1;
        if (b.isDefault) return 1;
        return 0;
      });
  }

  public getDefaultPaymentMethod(userId: string): PaymentMethod | null {
    return Array.from(this.paymentMethods.values()).find(method => method.isDefault) || null;
  }

  // Payment Processing
  public async createPaymentIntent(
    orderId: string,
    amount: number,
    customerId: string,
    vendorId: string,
    paymentMethodId: string
  ): Promise<PaymentIntent> {
    const paymentIntent: PaymentIntent = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      amount,
      currency: this.configuration.currency,
      orderId,
      customerId,
      vendorId,
      paymentMethodId,
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    this.paymentIntents.set(paymentIntent.id, paymentIntent);
    await this.saveData();

    return paymentIntent;
  }

  public async processPayment(paymentIntentId: string): Promise<PaymentResult> {
    const paymentIntent = this.paymentIntents.get(paymentIntentId);
    if (!paymentIntent) {
      return { success: false, error: 'Payment intent not found' };
    }

    const paymentMethod = this.paymentMethods.get(paymentIntent.paymentMethodId);
    if (!paymentMethod) {
      return { success: false, error: 'Payment method not found' };
    }

    try {
      // Update status to processing
      paymentIntent.status = 'processing';
      paymentIntent.updatedAt = new Date().toISOString();
      this.paymentIntents.set(paymentIntentId, paymentIntent);

      // Simulate payment processing based on method type
      const result = await this.processPaymentByType(paymentMethod, paymentIntent);

      // Update final status
      paymentIntent.status = result.success ? 'succeeded' : 'failed';
      paymentIntent.errorMessage = result.error;
      paymentIntent.updatedAt = new Date().toISOString();
      this.paymentIntents.set(paymentIntentId, paymentIntent);

      await this.saveData();

      return {
        ...result,
        paymentIntent,
      };
    } catch (error) {
      paymentIntent.status = 'failed';
      paymentIntent.errorMessage = error instanceof Error ? error.message : 'Unknown error';
      paymentIntent.updatedAt = new Date().toISOString();
      this.paymentIntents.set(paymentIntentId, paymentIntent);

      await this.saveData();

      return {
        success: false,
        error: paymentIntent.errorMessage,
        paymentIntent,
      };
    }
  }

  private async processPaymentByType(
    paymentMethod: PaymentMethod,
    paymentIntent: PaymentIntent
  ): Promise<PaymentResult> {
    // Simulate different payment processing flows
    switch (paymentMethod.type) {
      case 'credit_card':
      case 'debit_card':
        return this.processCreditCardPayment(paymentMethod, paymentIntent);
      
      case 'paypal':
        return this.processPayPalPayment(paymentMethod, paymentIntent);
      
      case 'apple_pay':
        return this.processApplePayPayment(paymentMethod, paymentIntent);
      
      case 'google_pay':
        return this.processGooglePayPayment(paymentMethod, paymentIntent);
      
      case 'bank_transfer':
        return this.processBankTransferPayment(paymentMethod, paymentIntent);
      
      case 'buy_now_pay_later':
        return this.processBuyNowPayLaterPayment(paymentMethod, paymentIntent);
      
      default:
        throw new Error(`Unsupported payment method: ${paymentMethod.type}`);
    }
  }

  private async processCreditCardPayment(
    paymentMethod: PaymentMethod,
    paymentIntent: PaymentIntent
  ): Promise<PaymentResult> {
    // Simulate credit card processing
    await this.delay(2000);

    // Simulate random success/failure for demo
    const success = Math.random() > 0.1; // 90% success rate

    if (success) {
      return { success: true };
    } else {
      return { 
        success: false, 
        error: 'Card declined. Please try a different payment method.' 
      };
    }
  }

  private async processPayPalPayment(
    paymentMethod: PaymentMethod,
    paymentIntent: PaymentIntent
  ): Promise<PaymentResult> {
    // Simulate PayPal processing
    await this.delay(1500);

    return {
      success: true,
      requiresAction: true,
      actionUrl: `https://paypal.com/checkout/${paymentIntent.id}`,
    };
  }

  private async processApplePayPayment(
    paymentMethod: PaymentMethod,
    paymentIntent: PaymentIntent
  ): Promise<PaymentResult> {
    // Simulate Apple Pay processing
    await this.delay(1000);
    return { success: true };
  }

  private async processGooglePayPayment(
    paymentMethod: PaymentMethod,
    paymentIntent: PaymentIntent
  ): Promise<PaymentResult> {
    // Simulate Google Pay processing
    await this.delay(1000);
    return { success: true };
  }

  private async processBankTransferPayment(
    paymentMethod: PaymentMethod,
    paymentIntent: PaymentIntent
  ): Promise<PaymentResult> {
    // Bank transfers typically require manual verification
    await this.delay(500);
    return {
      success: true,
      requiresAction: true,
      actionUrl: `bank://transfer/${paymentIntent.id}`,
    };
  }

  private async processBuyNowPayLaterPayment(
    paymentMethod: PaymentMethod,
    paymentIntent: PaymentIntent
  ): Promise<PaymentResult> {
    // Simulate BNPL processing
    await this.delay(2000);
    return { success: true };
  }

  // Refunds
  public async processRefund(refundRequest: RefundRequest): Promise<PaymentResult> {
    const paymentIntent = this.paymentIntents.get(refundRequest.paymentIntentId);
    if (!paymentIntent) {
      return { success: false, error: 'Payment intent not found' };
    }

    if (paymentIntent.status !== 'succeeded') {
      return { success: false, error: 'Can only refund successful payments' };
    }

    // Simulate refund processing
    await this.delay(1000);

    // In a real implementation, this would call the payment provider's refund API
    return { success: true };
  }

  // Utility Methods
  private generatePaymentMethodName(type: PaymentMethod['type'], data: any): string {
    switch (type) {
      case 'credit_card':
      case 'debit_card':
        return `${data.brand || 'Card'} ending in ${data.last4}`;
      case 'paypal':
        return `PayPal (${data.metadata?.email || 'Account'})`;
      case 'apple_pay':
        return 'Apple Pay';
      case 'google_pay':
        return 'Google Pay';
      case 'bank_transfer':
        return `${data.metadata?.bankName || 'Bank'} Transfer`;
      case 'buy_now_pay_later':
        return 'Buy Now, Pay Later';
      default:
        return 'Payment Method';
    }
  }

  private generateDisplayName(type: PaymentMethod['type'], data: any): string {
    switch (type) {
      case 'credit_card':
      case 'debit_card':
        return `•••• ${data.last4}`;
      case 'paypal':
        return 'PayPal';
      case 'apple_pay':
        return 'Apple Pay';
      case 'google_pay':
        return 'Google Pay';
      case 'bank_transfer':
        return 'Bank Transfer';
      case 'buy_now_pay_later':
        return 'BNPL';
      default:
        return 'Payment';
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async initializePaymentProviders() {
    // Initialize payment providers based on configuration
    // This would typically involve setting up Stripe, PayPal, etc.
    console.log('Initializing payment providers...');
  }

  // Storage
  private async loadStoredData() {
    try {
      const [methodsData, intentsData, configData] = await Promise.all([
        storage.getItem('paymentMethods'),
        storage.getItem('paymentIntents'),
        storage.getItem('paymentConfiguration'),
      ]);

      if (methodsData) {
        const methods = JSON.parse(methodsData);
        this.paymentMethods = new Map(Object.entries(methods));
      }

      if (intentsData) {
        const intents = JSON.parse(intentsData);
        this.paymentIntents = new Map(Object.entries(intents));
      }

      if (configData) {
        this.configuration = { ...this.configuration, ...JSON.parse(configData) };
      }
    } catch (error) {
      console.error('Error loading payment data:', error);
    }
  }

  private async saveData() {
    try {
      const methodsObj = Object.fromEntries(this.paymentMethods);
      const intentsObj = Object.fromEntries(this.paymentIntents);

      await Promise.all([
        storage.setItem('paymentMethods', JSON.stringify(methodsObj)),
        storage.setItem('paymentIntents', JSON.stringify(intentsObj)),
        storage.setItem('paymentConfiguration', JSON.stringify(this.configuration)),
      ]);
    } catch (error) {
      console.error('Error saving payment data:', error);
    }
  }

  // Public utility methods
  public getPaymentIntent(paymentIntentId: string): PaymentIntent | undefined {
    return this.paymentIntents.get(paymentIntentId);
  }

  public getPaymentIntentsByOrder(orderId: string): PaymentIntent[] {
    return Array.from(this.paymentIntents.values())
      .filter(intent => intent.orderId === orderId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  public isPaymentMethodSupported(type: PaymentMethod['type']): boolean {
    return this.configuration.enabledMethods.includes(type);
  }

  public formatAmount(amount: number): string {
    // Always use BHD formatting - amounts are in fils (1/1000 of BHD)
    try {
      return new Intl.NumberFormat(CURRENCY.LOCALE, {
        style: 'currency',
        currency: CURRENCY.CODE,
        minimumFractionDigits: CURRENCY.DECIMAL_PLACES,
        maximumFractionDigits: CURRENCY.DECIMAL_PLACES,
      }).format(amount / CURRENCY.SUBUNIT_RATIO);
    } catch (error) {
      // Fallback formatting with BHD
      return `${CURRENCY.SYMBOL} ${(amount / CURRENCY.SUBUNIT_RATIO).toFixed(CURRENCY.DECIMAL_PLACES)}`;
    }
  }
}

export default PaymentService.getInstance();
