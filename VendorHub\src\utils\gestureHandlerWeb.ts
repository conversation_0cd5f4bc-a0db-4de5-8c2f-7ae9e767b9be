// Web-specific gesture handler configuration
import { Platform } from 'react-native';

// Configure gesture handler for web compatibility
export const configureGestureHandlerWeb = () => {
  if (Platform.OS === 'web') {
    // Suppress gesture handler warnings on web
    const originalWarn = console.warn;
    console.warn = (...args) => {
      const message = args[0];
      if (typeof message === 'string') {
        // Suppress gesture handler specific warnings
        if (
          message.includes('GestureHandlerRootView') ||
          message.includes('Swipeable') ||
          message.includes('PanGestureHandler') ||
          message.includes('TapGestureHandler') ||
          message.includes('LongPressGestureHandler') ||
          message.includes('PinchGestureHandler') ||
          message.includes('RotationGestureHandler') ||
          message.includes('FlingGestureHandler')
        ) {
          return; // Suppress these warnings
        }
      }
      originalWarn.apply(console, args);
    };

    // Add CSS for web gesture handling
    if (typeof document !== 'undefined') {
      const style = document.createElement('style');
      style.textContent = `
        /* Web-specific gesture styles */
        .gesture-handler-web {
          touch-action: manipulation;
          user-select: none;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
        }
        
        .swipeable-web {
          overflow: hidden;
          position: relative;
        }
        
        .swipeable-web:hover .swipe-actions {
          opacity: 0.7;
          transform: translateX(0);
        }
        
        .swipe-actions {
          position: absolute;
          top: 0;
          bottom: 0;
          opacity: 0;
          transition: opacity 0.2s ease, transform 0.2s ease;
        }
        
        .swipe-actions-left {
          left: 0;
          transform: translateX(-100%);
        }
        
        .swipe-actions-right {
          right: 0;
          transform: translateX(100%);
        }
        
        .long-press-web {
          cursor: pointer;
          transition: transform 0.1s ease;
        }
        
        .long-press-web:active {
          transform: scale(0.95);
        }
        
        .zoomable-web {
          cursor: zoom-in;
          transition: transform 0.2s ease;
        }
        
        .zoomable-web.zoomed {
          cursor: zoom-out;
        }
        
        /* Disable text selection during gestures */
        .gesture-active {
          user-select: none !important;
          -webkit-user-select: none !important;
          -moz-user-select: none !important;
          -ms-user-select: none !important;
        }
        
        /* Custom scrollbar for better UX */
        .gesture-container::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        
        .gesture-container::-webkit-scrollbar-track {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 4px;
        }
        
        .gesture-container::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.3);
          border-radius: 4px;
        }
        
        .gesture-container::-webkit-scrollbar-thumb:hover {
          background: rgba(0, 0, 0, 0.5);
        }
      `;
      document.head.appendChild(style);
    }
  }
};

// Web-compatible gesture event handlers
export const createWebGestureHandlers = (options: {
  onPress?: () => void;
  onLongPress?: () => void;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onPinch?: (scale: number) => void;
  longPressDuration?: number;
}) => {
  if (Platform.OS !== 'web') {
    return {};
  }

  const {
    onPress,
    onLongPress,
    onSwipeLeft,
    onSwipeRight,
    onPinch,
    longPressDuration = 500,
  } = options;

  let longPressTimer: NodeJS.Timeout | null = null;
  let startX = 0;
  let startY = 0;
  let startTime = 0;
  let isLongPress = false;

  return {
    onMouseDown: (event: any) => {
      startX = event.clientX;
      startY = event.clientY;
      startTime = Date.now();
      isLongPress = false;

      if (onLongPress) {
        longPressTimer = setTimeout(() => {
          isLongPress = true;
          onLongPress();
        }, longPressDuration);
      }

      // Add gesture-active class
      if (event.target) {
        event.target.classList.add('gesture-active');
      }
    },

    onMouseUp: (event: any) => {
      const endX = event.clientX;
      const endY = event.clientY;
      const endTime = Date.now();
      const deltaX = endX - startX;
      const deltaY = endY - startY;
      const deltaTime = endTime - startTime;

      // Clear long press timer
      if (longPressTimer) {
        clearTimeout(longPressTimer);
        longPressTimer = null;
      }

      // Remove gesture-active class
      if (event.target) {
        event.target.classList.remove('gesture-active');
      }

      // Handle swipe gestures
      const minSwipeDistance = 50;
      const maxSwipeTime = 300;

      if (deltaTime < maxSwipeTime && Math.abs(deltaX) > minSwipeDistance && Math.abs(deltaY) < 100) {
        if (deltaX > 0 && onSwipeRight) {
          onSwipeRight();
          return;
        } else if (deltaX < 0 && onSwipeLeft) {
          onSwipeLeft();
          return;
        }
      }

      // Handle tap/press
      if (!isLongPress && deltaTime < 300 && Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10) {
        if (onPress) {
          onPress();
        }
      }
    },

    onMouseLeave: (event: any) => {
      // Clear long press timer
      if (longPressTimer) {
        clearTimeout(longPressTimer);
        longPressTimer = null;
      }

      // Remove gesture-active class
      if (event.target) {
        event.target.classList.remove('gesture-active');
      }
    },

    onWheel: (event: any) => {
      if (onPinch && event.ctrlKey) {
        event.preventDefault();
        const scale = event.deltaY > 0 ? 0.9 : 1.1;
        onPinch(scale);
      }
    },

    onTouchStart: (event: any) => {
      if (event.touches.length === 1) {
        const touch = event.touches[0];
        startX = touch.clientX;
        startY = touch.clientY;
        startTime = Date.now();
        isLongPress = false;

        if (onLongPress) {
          longPressTimer = setTimeout(() => {
            isLongPress = true;
            onLongPress();
          }, longPressDuration);
        }
      }
    },

    onTouchEnd: (event: any) => {
      if (event.changedTouches.length === 1) {
        const touch = event.changedTouches[0];
        const endX = touch.clientX;
        const endY = touch.clientY;
        const endTime = Date.now();
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        const deltaTime = endTime - startTime;

        // Clear long press timer
        if (longPressTimer) {
          clearTimeout(longPressTimer);
          longPressTimer = null;
        }

        // Handle swipe gestures
        const minSwipeDistance = 50;
        const maxSwipeTime = 300;

        if (deltaTime < maxSwipeTime && Math.abs(deltaX) > minSwipeDistance && Math.abs(deltaY) < 100) {
          if (deltaX > 0 && onSwipeRight) {
            onSwipeRight();
            return;
          } else if (deltaX < 0 && onSwipeLeft) {
            onSwipeLeft();
            return;
          }
        }

        // Handle tap/press
        if (!isLongPress && deltaTime < 300 && Math.abs(deltaX) < 10 && Math.abs(deltaY) < 10) {
          if (onPress) {
            onPress();
          }
        }
      }
    },
  };
};

// Initialize web gesture configuration
if (Platform.OS === 'web') {
  configureGestureHandlerWeb();
}
