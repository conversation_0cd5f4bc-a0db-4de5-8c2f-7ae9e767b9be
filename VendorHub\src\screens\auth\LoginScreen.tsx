import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth, useThemedStyles, useI18n } from '../../hooks';
import { Button, Card, Input, RTLView, RTLText, RTLScrollView } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  GRADIENTS,
} from '../../constants/theme';
import { APP_NAME, DEFAULT_ADMIN, USER_ROLES } from '../../constants';
import { validateEmail } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { UserRole } from '../../constants';

interface LoginScreenProps {
  navigation: any;
  route: {
    params?: {
      role?: UserRole;
    };
  };
}

export const LoginScreen: React.FC<LoginScreenProps> = ({ navigation, route }) => {
  const styles = useThemedStyles(createStyles);
  const { login, isLoading } = useAuth();
  const { t } = useI18n();
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});

  const selectedRole = route.params?.role;

  const validateForm = () => {
    const newErrors: { email?: string; password?: string } = {};

    if (!email.trim()) {
      newErrors.email = t('auth.emailRequired');
    } else if (!validateEmail(email)) {
      newErrors.email = t('auth.emailInvalid');
    }

    if (!password.trim()) {
      newErrors.password = t('auth.passwordRequired');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    try {
      const result = await login(email.trim(), password);
      
      if (result.success) {
        // Navigation will be handled by the auth state change
      } else {
        Alert.alert('Login Failed', result.message || 'Please check your credentials');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred');
    }
  };

  const handleForgotPassword = () => {
    Alert.alert(
      'Forgot Password',
      'Password reset functionality would be implemented here'
    );
  };

  const handleRegister = () => {
    navigation.navigate('Register', { role: selectedRole });
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const fillDemoCredentials = () => {
    if (selectedRole === USER_ROLES.ADMIN) {
      setEmail(DEFAULT_ADMIN.email);
      setPassword(DEFAULT_ADMIN.password);
    } else {
      setEmail('<EMAIL>');
      setPassword('password123');
    }
  };

  return (
    <LinearGradient colors={GRADIENTS.primary} style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          style={styles.keyboardAvoid}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <RTLScrollView
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* Header */}
            <RTLView style={styles.header}>
              <RTLText style={styles.logoText} weight="bold">{APP_NAME}</RTLText>
              <RTLText style={styles.subtitle} weight="medium">
                {selectedRole ? `${selectedRole} ${t('auth.login')}` : t('auth.welcome')}
              </RTLText>
              {selectedRole && (
                <RTLText style={styles.roleDescription}>
                  {getRoleDescription(selectedRole)}
                </RTLText>
              )}
            </RTLView>

            {/* Login Form */}
            <Card variant="glass" style={styles.formCard}>
              <RTLView style={styles.form}>
                <Input
                  label={t('auth.email')}
                  value={email}
                  onChangeText={setEmail}
                  placeholder={t('auth.emailPlaceholder')}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  leftIcon="mail-outline"
                  error={errors.email}
                  required
                />

                <Input
                  label={t('auth.password')}
                  value={password}
                  onChangeText={setPassword}
                  placeholder={t('auth.passwordPlaceholder')}
                  secureTextEntry
                  leftIcon="lock-closed-outline"
                  error={errors.password}
                  required
                />

                <Button
                  title={isLoading ? t('auth.signingIn') : t('auth.signIn')}
                  onPress={handleLogin}
                  loading={isLoading}
                  disabled={isLoading}
                  style={styles.loginButton}
                />

                <Button
                  title={t('auth.forgotPassword')}
                  onPress={handleForgotPassword}
                  variant="ghost"
                  style={styles.forgotButton}
                  textStyle={styles.forgotButtonText}
                />
              </RTLView>
            </Card>

            {/* Demo Credentials */}
            <Card variant="glass" style={styles.demoCard}>
              <RTLText style={styles.demoTitle} weight="medium">{t('demo.demoCredentials')}</RTLText>
              <RTLText style={styles.demoText}>
                {selectedRole === USER_ROLES.ADMIN
                  ? `${t('demo.adminCredentials')}: ${DEFAULT_ADMIN.email} / ${DEFAULT_ADMIN.password}`
                  : `${t('demo.userCredentials')}: <EMAIL> / password123`}
              </RTLText>
              <Button
                title={t('demo.fillDemoCredentials')}
                onPress={fillDemoCredentials}
                variant="outline"
                size="small"
                style={styles.demoButton}
                textStyle={styles.demoButtonText}
              />
            </Card>

            {/* Actions */}
            <RTLView style={styles.actionsContainer}>
              <Button
                title={t('auth.dontHaveAccount')}
                onPress={handleRegister}
                variant="ghost"
                style={styles.registerButton}
                textStyle={styles.registerButtonText}
              />
              
              <Button
                title={t('common.back')}
                onPress={handleBack}
                variant="ghost"
                style={styles.backButton}
                textStyle={styles.backButtonText}
              />
            </RTLView>
          </RTLScrollView>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </LinearGradient>
  );
};

const getRoleDescription = (role: UserRole): string => {
  switch (role) {
    case USER_ROLES.ADMIN:
      return 'Access admin dashboard and manage the platform';
    case USER_ROLES.VENDOR:
      return 'Manage your store and sell products';
    case USER_ROLES.CUSTOMER:
      return 'Shop from multiple vendors';
    default:
      return '';
  }
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoid: {
      flex: 1,
    },
    scrollContent: {
      flexGrow: 1,
      paddingHorizontal: SPACING.lg,
      paddingBottom: SPACING.xl,
    },
    header: {
      alignItems: 'center',
      paddingTop: SPACING.xl,
      paddingBottom: SPACING.lg,
    },
    logoText: {
      fontSize: FONT_SIZES.xxxl,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textOnPrimary,
      marginBottom: SPACING.sm,
    },
    subtitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textOnPrimary,
      marginBottom: SPACING.xs,
      opacity: 0.9,
    },
    roleDescription: {
      fontSize: FONT_SIZES.sm,
      color: colors.textOnPrimary,
      textAlign: 'center',
      opacity: 0.8,
    },
    formCard: {
      marginBottom: SPACING.lg,
    },
    form: {
      paddingVertical: SPACING.md,
    },
    loginButton: {
      marginTop: SPACING.md,
      marginBottom: SPACING.sm,
    },
    forgotButton: {
      backgroundColor: 'transparent',
    },
    forgotButtonText: {
      color: colors.textOnPrimary,
      opacity: 0.8,
    },
    demoCard: {
      marginBottom: SPACING.lg,
      alignItems: 'center',
    },
    demoTitle: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textOnPrimary,
      marginBottom: SPACING.xs,
    },
    demoText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textOnPrimary,
      textAlign: 'center',
      opacity: 0.8,
      marginBottom: SPACING.md,
    },
    demoButton: {
      borderColor: colors.textOnPrimary,
    },
    demoButtonText: {
      color: colors.textOnPrimary,
    },
    actionsContainer: {
      alignItems: 'center',
      paddingTop: SPACING.md,
    },
    registerButton: {
      backgroundColor: 'transparent',
      marginBottom: SPACING.sm,
    },
    registerButtonText: {
      color: colors.textOnPrimary,
    },
    backButton: {
      backgroundColor: 'transparent',
    },
    backButtonText: {
      color: colors.textOnPrimary,
      opacity: 0.7,
    },
  });
