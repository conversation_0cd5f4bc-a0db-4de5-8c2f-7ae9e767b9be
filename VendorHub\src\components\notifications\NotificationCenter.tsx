import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Modal,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles, useAuth } from '../../hooks';
import { Card } from '../Card';
import { Button } from '../Button';
import { EmptyState } from '../EmptyState';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../../constants/theme';
import { notificationService, NotificationData } from '../../services/NotificationService';
import type { ThemeColors } from '../../contexts/ThemeContext';

export interface NotificationCenterProps {
  visible: boolean;
  onClose: () => void;
  onNotificationPress?: (notification: NotificationData) => void;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({
  visible,
  onClose,
  onNotificationPress,
}) => {
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [filter, setFilter] = useState<'all' | 'unread'>('all');

  useEffect(() => {
    if (visible) {
      loadNotifications();
    }
  }, [visible]);

  const loadNotifications = () => {
    try {
      if (!user?.id) {
        setNotifications([]);
        return;
      }

      if (!notificationService || typeof notificationService.getUserNotifications !== 'function') {
        console.warn('NotificationService not available');
        setNotifications([]);
        return;
      }

      const userNotifications = notificationService.getUserNotifications(user.id);
      setNotifications(userNotifications);
    } catch (error) {
      console.error('Error loading notifications:', error);
      setNotifications([]);
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'unread') {
      return !notification.read;
    }
    return true;
  });

  const handleNotificationPress = async (notification: NotificationData) => {
    if (!user?.id) return;

    // Mark as read
    await notificationService.markAsRead(notification.id, user.id);
    setNotifications(prev =>
      prev.map(n => n.id === notification.id ? { ...n, read: true } : n)
    );

    // Call external handler
    onNotificationPress?.(notification);
  };

  const handleMarkAllRead = async () => {
    if (!user?.id) return;

    await notificationService.markAllAsRead(user.id);
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const handleDeleteNotification = (notificationId: string) => {
    Alert.alert(
      'Delete Notification',
      'Are you sure you want to delete this notification?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            notificationService.deleteNotification(notificationId);
            setNotifications(prev => prev.filter(n => n.id !== notificationId));
          },
        },
      ]
    );
  };

  const handleClearAll = () => {
    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to clear all notifications?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: () => {
            notificationService.clearAllNotifications();
            setNotifications([]);
          },
        },
      ]
    );
  };

  const getNotificationIcon = (type: NotificationData['type']) => {
    switch (type) {
      case 'order':
        return 'receipt-outline';
      case 'vendor':
        return 'storefront-outline';
      case 'product':
        return 'cube-outline';
      case 'promotion':
        return 'pricetag-outline';
      case 'system':
        return 'settings-outline';
      default:
        return 'notifications-outline';
    }
  };

  const getNotificationColor = (type: NotificationData['type']) => {
    switch (type) {
      case 'order':
        return '#4CAF50';
      case 'vendor':
        return '#2196F3';
      case 'product':
        return '#FF9800';
      case 'promotion':
        return '#E91E63';
      case 'system':
        return '#9C27B0';
      default:
        return '#667eea';
    }
  };

  const getPriorityColor = (priority: NotificationData['priority']) => {
    switch (priority) {
      case 'high':
        return '#FF6B6B';
      case 'normal':
        return '#667eea';
      case 'low':
        return '#CCCCCC';
      default:
        return '#667eea';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const renderNotification = ({ item }: { item: NotificationData }) => (
    <Card style={[styles.notificationCard, !item.read && styles.unreadCard]} variant="outlined">
      <TouchableOpacity
        style={styles.notificationContent}
        onPress={() => handleNotificationPress(item)}
      >
        <View style={styles.notificationHeader}>
          <View style={styles.notificationIcon}>
            <View style={[styles.iconContainer, { backgroundColor: getNotificationColor(item.type) }]}>
              <Ionicons name={getNotificationIcon(item.type)} size={20} color="#FFFFFF" />
            </View>
            {item.priority === 'high' && (
              <View style={[styles.priorityBadge, { backgroundColor: getPriorityColor(item.priority) }]}>
                <Ionicons name="alert" size={12} color="#FFFFFF" />
              </View>
            )}
          </View>

          <View style={styles.notificationInfo}>
            <Text style={[styles.notificationTitle, !item.read && styles.unreadTitle]}>
              {item.title}
            </Text>
            <Text style={styles.notificationBody} numberOfLines={2}>
              {item.body}
            </Text>
            <Text style={styles.notificationTime}>
              {formatTimestamp(item.timestamp)}
            </Text>
          </View>

          <View style={styles.notificationActions}>
            {!item.read && <View style={styles.unreadDot} />}
            <TouchableOpacity
              style={styles.deleteButton}
              onPress={() => handleDeleteNotification(item.id)}
            >
              <Ionicons name="close" size={16} color="#CCCCCC" />
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    </Card>
  );

  const renderHeader = () => (
    <View style={styles.header}>
      <Text style={styles.headerTitle}>Notifications</Text>
      <TouchableOpacity onPress={onClose}>
        <Ionicons name="close" size={24} color="#667eea" />
      </TouchableOpacity>
    </View>
  );

  const renderFilters = () => (
    <View style={styles.filters}>
      <View style={styles.filterButtons}>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'all' && styles.filterButtonActive]}
          onPress={() => setFilter('all')}
        >
          <Text style={[styles.filterButtonText, filter === 'all' && styles.filterButtonTextActive]}>
            All ({notifications.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, filter === 'unread' && styles.filterButtonActive]}
          onPress={() => setFilter('unread')}
        >
          <Text style={[styles.filterButtonText, filter === 'unread' && styles.filterButtonTextActive]}>
            Unread ({notifications.filter(n => !n.read).length})
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.actionButtons}>
        {notifications.some(n => !n.read) && (
          <TouchableOpacity style={styles.actionButton} onPress={handleMarkAllRead}>
            <Text style={styles.actionButtonText}>Mark All Read</Text>
          </TouchableOpacity>
        )}
        {notifications.length > 0 && (
          <TouchableOpacity style={styles.actionButton} onPress={handleClearAll}>
            <Text style={[styles.actionButtonText, styles.destructiveText]}>Clear All</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {renderHeader()}
        {renderFilters()}

        <FlatList
          data={filteredNotifications}
          renderItem={renderNotification}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          ListEmptyComponent={
            <EmptyState
              icon="notifications-outline"
              title={filter === 'unread' ? 'No unread notifications' : 'No notifications'}
              description={
                filter === 'unread'
                  ? 'All caught up! No new notifications to read.'
                  : 'You\'ll see notifications here when they arrive.'
              }
            />
          }
          showsVerticalScrollIndicator={false}
        />
      </View>
    </Modal>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
    },
    filters: {
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    filterButtons: {
      flexDirection: 'row',
      marginBottom: SPACING.sm,
    },
    filterButton: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      backgroundColor: colors.surface,
      marginRight: SPACING.sm,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterButtonActive: {
      backgroundColor: '#667eea',
      borderColor: '#667eea',
    },
    filterButtonText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    filterButtonTextActive: {
      color: '#FFFFFF',
    },
    actionButtons: {
      flexDirection: 'row',
      gap: SPACING.md,
    },
    actionButton: {
      paddingVertical: SPACING.xs,
    },
    actionButtonText: {
      fontSize: FONT_SIZES.sm,
      color: '#667eea',
      fontWeight: FONT_WEIGHTS.medium,
    },
    destructiveText: {
      color: '#FF6B6B',
    },
    listContent: {
      padding: SPACING.lg,
    },
    notificationCard: {
      marginBottom: SPACING.md,
    },
    unreadCard: {
      borderLeftWidth: 4,
      borderLeftColor: '#667eea',
    },
    notificationContent: {
      padding: SPACING.md,
    },
    notificationHeader: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    notificationIcon: {
      position: 'relative',
      marginRight: SPACING.md,
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    priorityBadge: {
      position: 'absolute',
      top: -4,
      right: -4,
      width: 16,
      height: 16,
      borderRadius: 8,
      justifyContent: 'center',
      alignItems: 'center',
    },
    notificationInfo: {
      flex: 1,
      marginRight: SPACING.sm,
    },
    notificationTitle: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    unreadTitle: {
      fontWeight: FONT_WEIGHTS.bold,
    },
    notificationBody: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      lineHeight: 18,
      marginBottom: SPACING.xs,
    },
    notificationTime: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      opacity: 0.8,
    },
    notificationActions: {
      alignItems: 'center',
    },
    unreadDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#667eea',
      marginBottom: SPACING.sm,
    },
    deleteButton: {
      padding: SPACING.xs,
    },
  });
