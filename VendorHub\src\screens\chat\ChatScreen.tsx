import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  FlatList,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles, useAuth } from '../../hooks';
import { Card, Button } from '../../components';
import ChatService, { ChatMessage, ChatConversation } from '../../services/ChatService';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';

interface ChatScreenProps {
  navigation: any;
  route: {
    params: {
      chatId?: string;
      otherUserId?: string;
      otherUserName?: string;
      otherUserAvatar?: string;
    };
  };
}

export const ChatScreen: React.FC<ChatScreenProps> = ({ navigation, route }) => {
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const flatListRef = useRef<FlatList>(null);
  
  const [conversation, setConversation] = useState<ChatConversation | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const { chatId, otherUserId, otherUserName, otherUserAvatar } = route.params;

  useEffect(() => {
    initializeChat();
    setupEventListeners();

    return () => {
      ChatService.removeAllListeners();
    };
  }, []);

  useEffect(() => {
    if (conversation && user) {
      // Mark messages as read when screen is focused
      ChatService.markMessagesAsRead(conversation.id, user.id);
    }
  }, [conversation, user]);

  const initializeChat = async () => {
    if (!user) return;

    try {
      let conv: ChatConversation;

      if (chatId) {
        // Load existing conversation
        const existingConv = ChatService.getConversation(chatId);
        if (!existingConv) {
          Alert.alert('Error', 'Conversation not found');
          navigation.goBack();
          return;
        }
        conv = existingConv;
      } else if (otherUserId && otherUserName) {
        // Create or get conversation with specific user
        conv = await ChatService.getOrCreateConversation(
          otherUserId,
          otherUserName,
          otherUserAvatar
        );
      } else {
        Alert.alert('Error', 'Invalid chat parameters');
        navigation.goBack();
        return;
      }

      setConversation(conv);
      setMessages(ChatService.getMessages(conv.id));
      setIsLoading(false);

      // Set navigation title
      const otherParticipant = conv.participants.find(id => id !== user.id);
      const title = otherParticipant ? conv.participantNames[otherParticipant] : 'Chat';
      navigation.setOptions({ title });

    } catch (error) {
      console.error('Error initializing chat:', error);
      Alert.alert('Error', 'Failed to load chat');
      navigation.goBack();
    }
  };

  const setupEventListeners = () => {
    ChatService.on('messageReceived', handleMessageReceived);
    ChatService.on('typingStarted', handleTypingStarted);
    ChatService.on('typingStopped', handleTypingStopped);
    ChatService.on('messageUpdated', handleMessageUpdated);
    ChatService.on('messageDeleted', handleMessageDeleted);
  };

  const handleMessageReceived = (message: ChatMessage) => {
    if (conversation && message.chatId === conversation.id) {
      setMessages(prev => [...prev, message]);
      scrollToBottom();
    }
  };

  const handleTypingStarted = (indicator: any) => {
    if (conversation && indicator.chatId === conversation.id && indicator.userId !== user?.id) {
      setTypingUsers(prev => {
        if (!prev.includes(indicator.userName)) {
          return [...prev, indicator.userName];
        }
        return prev;
      });
    }
  };

  const handleTypingStopped = (data: any) => {
    if (conversation && data.chatId === conversation.id) {
      setTypingUsers(prev => prev.filter(name => name !== data.userName));
    }
  };

  const handleMessageUpdated = (message: ChatMessage) => {
    if (conversation && message.chatId === conversation.id) {
      setMessages(prev => prev.map(m => m.id === message.id ? message : m));
    }
  };

  const handleMessageDeleted = (data: any) => {
    if (conversation && data.chatId === conversation.id) {
      setMessages(prev => prev.filter(m => m.id !== data.messageId));
    }
  };

  const sendMessage = async () => {
    if (!inputText.trim() || !conversation || !user) return;

    const messageText = inputText.trim();
    setInputText('');
    stopTyping();

    try {
      await ChatService.sendMessage(conversation.id, messageText);
      scrollToBottom();
    } catch (error) {
      console.error('Error sending message:', error);
      Alert.alert('Error', 'Failed to send message');
      setInputText(messageText); // Restore input text
    }
  };

  const startTyping = () => {
    if (!isTyping && conversation) {
      setIsTyping(true);
      ChatService.startTyping(conversation.id);
    }
  };

  const stopTyping = () => {
    if (isTyping && conversation) {
      setIsTyping(false);
      ChatService.stopTyping(conversation.id);
    }
  };

  const scrollToBottom = () => {
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const handleLongPress = (message: ChatMessage) => {
    if (message.senderId === user?.id) {
      Alert.alert(
        'Message Options',
        'What would you like to do?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Edit',
            onPress: () => editMessage(message),
          },
          {
            text: 'Delete',
            style: 'destructive',
            onPress: () => deleteMessage(message),
          },
        ]
      );
    }
  };

  const editMessage = (message: ChatMessage) => {
    Alert.prompt(
      'Edit Message',
      'Enter new message:',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Save',
          onPress: (newText) => {
            if (newText && newText.trim()) {
              ChatService.editMessage(message.id, newText.trim());
            }
          },
        },
      ],
      'plain-text',
      message.content
    );
  };

  const deleteMessage = (message: ChatMessage) => {
    Alert.alert(
      'Delete Message',
      'Are you sure you want to delete this message?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => ChatService.deleteMessage(message.id),
        },
      ]
    );
  };

  const renderMessage = ({ item, index }: { item: ChatMessage; index: number }) => {
    const isOwnMessage = item.senderId === user?.id;
    const showAvatar = !isOwnMessage && (index === 0 || messages[index - 1].senderId !== item.senderId);
    const showTimestamp = index === 0 || 
      new Date(item.timestamp).getTime() - new Date(messages[index - 1].timestamp).getTime() > 300000; // 5 minutes

    return (
      <View style={styles.messageContainer}>
        {showTimestamp && (
          <Text style={styles.timestamp}>
            {new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>
        )}
        
        <View style={[styles.messageRow, isOwnMessage && styles.ownMessageRow]}>
          {showAvatar && !isOwnMessage && (
            <View style={styles.avatarContainer}>
              {item.senderAvatar ? (
                <Image source={{ uri: item.senderAvatar }} style={styles.avatar} />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <Ionicons name="person" size={16} color="#666" />
                </View>
              )}
            </View>
          )}
          
          <TouchableOpacity
            style={[
              styles.messageBubble,
              isOwnMessage ? styles.ownMessageBubble : styles.otherMessageBubble,
            ]}
            onLongPress={() => handleLongPress(item)}
          >
            {!isOwnMessage && showAvatar && (
              <Text style={styles.senderName}>{item.senderName}</Text>
            )}
            
            <Text style={[
              styles.messageText,
              isOwnMessage ? styles.ownMessageText : styles.otherMessageText,
            ]}>
              {item.content}
            </Text>
            
            <View style={styles.messageFooter}>
              {item.edited && (
                <Text style={styles.editedLabel}>edited</Text>
              )}
              <Text style={[
                styles.messageTime,
                isOwnMessage ? styles.ownMessageTime : styles.otherMessageTime,
              ]}>
                {new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </Text>
              {isOwnMessage && (
                <Ionicons 
                  name={item.read ? "checkmark-done" : "checkmark"} 
                  size={12} 
                  color={item.read ? "#4CAF50" : "#999"} 
                />
              )}
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  const renderTypingIndicator = () => {
    if (typingUsers.length === 0) return null;

    return (
      <View style={styles.typingContainer}>
        <Text style={styles.typingText}>
          {typingUsers.join(', ')} {typingUsers.length === 1 ? 'is' : 'are'} typing...
        </Text>
      </View>
    );
  };

  const renderInputArea = () => (
    <View style={styles.inputContainer}>
      <View style={styles.inputRow}>
        <TouchableOpacity style={styles.attachButton}>
          <Ionicons name="add" size={24} color="#667eea" />
        </TouchableOpacity>
        
        <TextInput
          style={styles.textInput}
          value={inputText}
          onChangeText={(text) => {
            setInputText(text);
            if (text.length > 0) {
              startTyping();
            } else {
              stopTyping();
            }
          }}
          placeholder="Type a message..."
          placeholderTextColor="#999"
          multiline
          maxLength={1000}
          onBlur={stopTyping}
        />
        
        <TouchableOpacity 
          style={[styles.sendButton, inputText.trim() && styles.sendButtonActive]}
          onPress={sendMessage}
          disabled={!inputText.trim()}
        >
          <Ionicons 
            name="send" 
            size={20} 
            color={inputText.trim() ? "#FFFFFF" : "#999"} 
          />
        </TouchableOpacity>
      </View>
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading chat...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <FlatList
          ref={flatListRef}
          data={messages}
          renderItem={renderMessage}
          keyExtractor={(item) => item.id}
          style={styles.messagesList}
          contentContainerStyle={styles.messagesContent}
          onContentSizeChange={scrollToBottom}
          showsVerticalScrollIndicator={false}
        />
        
        {renderTypingIndicator()}
        {renderInputArea()}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.lg,
  },
  messageContainer: {
    marginBottom: SPACING.md,
  },
  timestamp: {
    fontSize: FONT_SIZES.xs,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.sm,
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  ownMessageRow: {
    justifyContent: 'flex-end',
  },
  avatarContainer: {
    marginRight: SPACING.sm,
    marginBottom: SPACING.xs,
  },
  avatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
  },
  avatarPlaceholder: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  messageBubble: {
    maxWidth: '75%',
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: BORDER_RADIUS.lg,
    marginBottom: SPACING.xs,
  },
  ownMessageBubble: {
    backgroundColor: '#667eea',
    borderBottomRightRadius: BORDER_RADIUS.sm,
  },
  otherMessageBubble: {
    backgroundColor: colors.surface,
    borderBottomLeftRadius: BORDER_RADIUS.sm,
  },
  senderName: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
    marginBottom: SPACING.xs,
  },
  messageText: {
    fontSize: FONT_SIZES.md,
    lineHeight: 20,
  },
  ownMessageText: {
    color: '#FFFFFF',
  },
  otherMessageText: {
    color: colors.text,
  },
  messageFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: SPACING.xs,
    gap: SPACING.xs,
  },
  editedLabel: {
    fontSize: FONT_SIZES.xs,
    fontStyle: 'italic',
    color: 'rgba(255, 255, 255, 0.7)',
  },
  messageTime: {
    fontSize: FONT_SIZES.xs,
  },
  ownMessageTime: {
    color: 'rgba(255, 255, 255, 0.7)',
  },
  otherMessageTime: {
    color: colors.textSecondary,
  },
  typingContainer: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
  },
  typingText: {
    fontSize: FONT_SIZES.sm,
    fontStyle: 'italic',
    color: colors.textSecondary,
  },
  inputContainer: {
    backgroundColor: colors.surface,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: SPACING.sm,
  },
  attachButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  textInput: {
    flex: 1,
    maxHeight: 100,
    minHeight: 40,
    backgroundColor: colors.background,
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    fontSize: FONT_SIZES.md,
    color: colors.text,
    textAlignVertical: 'center',
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonActive: {
    backgroundColor: '#667eea',
  },
});
