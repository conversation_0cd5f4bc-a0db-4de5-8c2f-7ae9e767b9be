import React, { useRef, useState } from 'react';
import {
  View,
  Image,
  StyleSheet,
  Dimensions,
  Animated,
  PanResponder,
  Modal,
  TouchableOpacity,
  StatusBar,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Haptics from 'expo-haptics';
import { useThemedStyles } from '../../hooks';
import { webCompatibleHaptics, isWeb } from '../../utils/webCompatibility';
import {
  SPACING,
  BORDER_RADIUS,
} from '../../constants/theme';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface ZoomableImageProps {
  source: { uri: string } | number;
  style?: any;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  enableFullscreen?: boolean;
  maxZoom?: number;
  minZoom?: number;
  doubleTapZoom?: number;
  onZoomStart?: () => void;
  onZoomEnd?: () => void;
  onFullscreenOpen?: () => void;
  onFullscreenClose?: () => void;
}

export const ZoomableImage: React.FC<ZoomableImageProps> = ({
  source,
  style,
  resizeMode = 'cover',
  enableFullscreen = true,
  maxZoom = 3,
  minZoom = 1,
  doubleTapZoom = 2,
  onZoomStart,
  onZoomEnd,
  onFullscreenOpen,
  onFullscreenClose,
}) => {
  const styles = useThemedStyles(createStyles);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
  
  // Animation values
  const scale = useRef(new Animated.Value(1)).current;
  const translateX = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  
  // Gesture state
  const lastScale = useRef(1);
  const lastTranslateX = useRef(0);
  const lastTranslateY = useRef(0);
  const isZooming = useRef(false);
  const lastTap = useRef(0);

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => !isWeb,
      onMoveShouldSetPanResponder: () => !isWeb,
      onPanResponderGrant: (evt) => {
        // Handle double tap
        const now = Date.now();
        if (now - lastTap.current < 300) {
          handleDoubleTap();
        }
        lastTap.current = now;
      },
      
      onPanResponderMove: (evt, gestureState) => {
        if (gestureState.numberActiveTouches === 2) {
          // Pinch to zoom
          handlePinchZoom(gestureState);
        } else if (gestureState.numberActiveTouches === 1 && lastScale.current > 1) {
          // Pan when zoomed
          handlePan(gestureState);
        }
      },
      
      onPanResponderRelease: () => {
        // Save current values
        lastScale.current = scale._value;
        lastTranslateX.current = translateX._value;
        lastTranslateY.current = translateY._value;
        
        // Snap back if zoomed out too much
        if (lastScale.current < minZoom) {
          resetZoom();
        }
        
        if (isZooming.current) {
          onZoomEnd?.();
          isZooming.current = false;
        }
      },
    })
  ).current;

  const handlePinchZoom = (gestureState: any) => {
    if (!isZooming.current) {
      onZoomStart?.();
      isZooming.current = true;
      webCompatibleHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    
    const newScale = Math.max(
      minZoom,
      Math.min(maxZoom, lastScale.current * gestureState.scale)
    );
    
    scale.setValue(newScale);
  };

  const handlePan = (gestureState: any) => {
    const newTranslateX = lastTranslateX.current + gestureState.dx;
    const newTranslateY = lastTranslateY.current + gestureState.dy;
    
    translateX.setValue(newTranslateX);
    translateY.setValue(newTranslateY);
  };

  const handleDoubleTap = () => {
    webCompatibleHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    const targetScale = lastScale.current > 1 ? 1 : doubleTapZoom;
    const targetTranslateX = targetScale === 1 ? 0 : lastTranslateX.current;
    const targetTranslateY = targetScale === 1 ? 0 : lastTranslateY.current;
    
    Animated.parallel([
      Animated.spring(scale, {
        toValue: targetScale,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }),
      Animated.spring(translateX, {
        toValue: targetTranslateX,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }),
      Animated.spring(translateY, {
        toValue: targetTranslateY,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }),
    ]).start(() => {
      lastScale.current = targetScale;
      lastTranslateX.current = targetTranslateX;
      lastTranslateY.current = targetTranslateY;
    });
  };

  const resetZoom = () => {
    Animated.parallel([
      Animated.spring(scale, {
        toValue: 1,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }),
      Animated.spring(translateX, {
        toValue: 0,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }),
      Animated.spring(translateY, {
        toValue: 0,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }),
    ]).start(() => {
      lastScale.current = 1;
      lastTranslateX.current = 0;
      lastTranslateY.current = 0;
    });
  };

  const openFullscreen = () => {
    if (!enableFullscreen) return;
    
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setIsFullscreen(true);
    onFullscreenOpen?.();
  };

  const closeFullscreen = () => {
    setIsFullscreen(false);
    resetZoom();
    onFullscreenClose?.();
  };

  const renderImage = (fullscreen = false) => (
    <Animated.View
      style={[
        fullscreen ? styles.fullscreenImageContainer : styles.imageContainer,
        {
          transform: [
            { scale },
            { translateX },
            { translateY },
          ],
        },
      ]}
      {...panResponder.panHandlers}
    >
      <Image
        source={source}
        style={fullscreen ? styles.fullscreenImage : [styles.image, style]}
        resizeMode={resizeMode}
        onLoad={(event) => {
          const { width, height } = event.nativeEvent.source;
          setImageSize({ width, height });
        }}
      />
    </Animated.View>
  );

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        onPress={openFullscreen}
        disabled={!enableFullscreen}
        activeOpacity={0.9}
      >
        {renderImage(false)}
      </TouchableOpacity>

      {/* Fullscreen Modal */}
      <Modal
        visible={isFullscreen}
        transparent
        animationType="fade"
        onRequestClose={closeFullscreen}
      >
        <StatusBar hidden />
        <View style={styles.fullscreenContainer}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={closeFullscreen}
            hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
          >
            <Ionicons name="close" size={30} color="#FFFFFF" />
          </TouchableOpacity>
          
          {renderImage(true)}
          
          <View style={styles.fullscreenHint}>
            <Ionicons name="resize" size={20} color="#FFFFFF" />
          </View>
        </View>
      </Modal>
    </View>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    overflow: 'hidden',
    borderRadius: BORDER_RADIUS.md,
  },
  imageContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
  },
  fullscreenContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullscreenImageContainer: {
    width: screenWidth,
    height: screenHeight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullscreenImage: {
    width: screenWidth,
    height: screenHeight,
  },
  closeButton: {
    position: 'absolute',
    top: 50,
    right: SPACING.lg,
    zIndex: 1000,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullscreenHint: {
    position: 'absolute',
    bottom: 50,
    alignSelf: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 20,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
  },
});

// Image Gallery component with swipe navigation
export interface ImageGalleryProps {
  images: string[];
  initialIndex?: number;
  onIndexChange?: (index: number) => void;
  style?: any;
}

export const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  initialIndex = 0,
  onIndexChange,
  style,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  
  const handleIndexChange = (index: number) => {
    setCurrentIndex(index);
    onIndexChange?.(index);
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
  };

  if (images.length === 0) return null;

  return (
    <View style={style}>
      <ZoomableImage
        source={{ uri: images[currentIndex] }}
        style={{ width: '100%', height: 200 }}
      />
      
      {images.length > 1 && (
        <View style={styles.galleryIndicators}>
          {images.map((_, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.indicator,
                index === currentIndex && styles.indicatorActive,
              ]}
              onPress={() => handleIndexChange(index)}
            />
          ))}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  galleryIndicators: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: SPACING.sm,
    gap: SPACING.xs,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  indicatorActive: {
    backgroundColor: '#667eea',
  },
});
