# Language Switching Guide for VendorHub

## Overview
VendorHub supports multiple languages with full RTL (Right-to-Left) support for Arabic. This guide explains how users can switch between languages and what happens during the process.

## Supported Languages

### Available Languages
VendorHub exclusively supports two languages, focusing on the target market:

1. **English (en)** - Default LTR language, international standard
2. **Arabic (ar)** - Standard Arabic with full RTL support

Both languages use **BHD (Bahraini Dinar)** as the exclusive currency with no currency exchange functionality.

## How to Switch Languages

### Method 1: Using the Language Selector in Header (Recommended)
1. **Open the app** and navigate to the Home screen
2. **Look for the language selector** in the top-right corner of the header (shows current language flag)
3. **Tap the language selector** to open the language selection modal
4. **Choose your preferred language** from the list
5. **The app will automatically restart** if switching between LTR and RTL languages

### Method 2: Programmatic Language Switching
For developers or advanced users, you can switch languages programmatically:

```typescript
import { useI18n } from '../hooks/useI18n';

const { changeLanguage } = useI18n();

// Switch to Arabic
await changeLanguage('ar');

// Switch to English
await changeLanguage('en');
```

### Method 3: Using the RTL Test Component (Development)
During development, you can use the RTL Test Component:
1. The component provides quick buttons to switch between English and Arabic
2. Useful for testing RTL functionality
3. Shows current language status and RTL state

## Language Switching Behavior

### Automatic App Restart
- **When switching between LTR and RTL languages**, the app automatically restarts
- This ensures proper RTL layout initialization
- **Examples**: English ↔ Arabic

### App Restart Always Required
- **With only English (LTR) and Arabic (RTL)**, switching languages always requires an app restart
- **This ensures proper RTL layout initialization** and optimal user experience

### What Happens During Language Switch
1. **Language preference is saved** to device storage
2. **RTL status is checked** for the new language
3. **If RTL status changes**:
   - I18nManager.forceRTL() is called
   - App restarts automatically
   - New language loads with proper layout
4. **If RTL status stays the same**:
   - Language changes immediately
   - UI updates without restart

## Visual Indicators

### Language Selector Appearance
- **Flag icon** showing current language
- **Dropdown arrow** indicating it's interactive
- **Semi-transparent background** in header
- **RTL badge** for right-to-left languages

### Language Selection Modal
- **Full-screen modal** with language options
- **Flag, English name, and native name** for each language
- **Checkmark** indicating current selection
- **RTL badge** for Arabic languages
- **Close button** to cancel selection

## RTL Language Features

### When Arabic is Selected
- **Text direction**: Right-to-left reading flow
- **Layout mirroring**: UI elements flip horizontally
- **Icon mirroring**: Directional icons (arrows, chevrons) flip
- **Typography**: Arabic fonts with proper line height
- **Navigation**: Back/forward buttons work in RTL context

### Arabic Language
- **Standard Arabic (ar)**: Formal Arabic suitable for all Arabic speakers
- **Culturally appropriate**: Translations are suitable for the Gulf region
- **Comprehensive coverage**: All app features fully translated

## User Experience

### Smooth Transitions
- **Loading states** during language switching
- **Preserved app state** after restart (when applicable)
- **Consistent navigation** across languages
- **Maintained user session** and data

### Accessibility
- **Screen reader support** for all languages
- **Proper focus management** in RTL mode
- **Keyboard navigation** works correctly
- **Touch targets** appropriately sized

## Troubleshooting

### Common Issues and Solutions

#### Language Not Changing
- **Check internet connection** (if translations are loaded remotely)
- **Restart the app manually** if automatic restart fails
- **Clear app cache** if persistent issues occur

#### Layout Issues in RTL
- **Force close and reopen** the app
- **Check device RTL settings** in system preferences
- **Update the app** to latest version

#### Missing Translations
- **Fallback to English** if translation not found
- **Report missing translations** to development team
- **Use English temporarily** until translations are added

### Developer Debugging
```typescript
// Check current language and RTL status
const { currentLanguage, isRTL } = useI18n();
console.log('Current Language:', currentLanguage);
console.log('Is RTL:', isRTL);

// Test translation keys
const translation = t('common.welcome');
console.log('Translation:', translation);
```

## Best Practices for Users

### Recommended Usage
1. **Choose your preferred language** on first app launch
2. **Stick with one language** for consistent experience
3. **Use Arabic** for natural RTL reading experience
4. **Switch to English** if you encounter any issues

### For Arabic Users
1. **Standard Arabic** provides excellent coverage for all Arabic speakers
2. **RTL layout** provides natural reading experience
3. **All features work identically** in Arabic and English
4. **Culturally appropriate** translations for the Gulf region

## Technical Details

### Language Persistence
- Language preference stored in device storage
- Survives app updates and device restarts
- Synchronized across app sessions

### Performance
- **Minimal impact** on app performance
- **Efficient font loading** for Arabic typography
- **Optimized RTL calculations** for smooth UI
- **Fast language switching** for same-direction languages

## Support

### Getting Help
- **Check this guide** for common questions
- **Use English temporarily** if experiencing issues
- **Contact support** with language-specific problems
- **Report bugs** related to RTL or translations

### Feedback
- **Translation improvements** are welcome
- **Cultural appropriateness** feedback appreciated
- **UI/UX suggestions** for better language experience
- **Accessibility improvements** for all languages

---

**Note**: The VendorHub app is designed to provide an authentic, culturally appropriate experience for users in Bahrain while maintaining excellent support for international users through English and Arabic language options.
