import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { useThemedStyles } from '../hooks';
import { BORDER_RADIUS, SPACING, FONT_SIZES, FONT_WEIGHTS } from '../constants/theme';
import { VENDOR_STATUS, ORDER_STATUS } from '../constants';
import type { ThemeColors } from '../contexts/ThemeContext';
import type { VendorStatus, OrderStatus } from '../constants';

interface StatusBadgeProps {
  status: VendorStatus | OrderStatus | string;
  type?: 'vendor' | 'order' | 'custom';
  size?: 'small' | 'medium' | 'large';
  style?: ViewStyle;
  customColor?: string;
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  type = 'custom',
  size = 'medium',
  style,
  customColor,
}) => {
  const styles = useThemedStyles(createStyles);

  const getStatusConfig = () => {
    if (type === 'vendor') {
      switch (status as VendorStatus) {
        case VENDOR_STATUS.APPROVED:
          return { color: styles.approved.backgroundColor, label: 'Approved' };
        case VENDOR_STATUS.PENDING:
          return { color: styles.pending.backgroundColor, label: 'Pending' };
        case VENDOR_STATUS.REJECTED:
          return { color: styles.rejected.backgroundColor, label: 'Rejected' };
        case VENDOR_STATUS.SUSPENDED:
          return { color: styles.suspended.backgroundColor, label: 'Suspended' };
        default:
          return { color: styles.default.backgroundColor, label: status };
      }
    }

    if (type === 'order') {
      switch (status as OrderStatus) {
        case ORDER_STATUS.PENDING:
          return { color: styles.pending.backgroundColor, label: 'Pending' };
        case ORDER_STATUS.CONFIRMED:
          return { color: styles.confirmed.backgroundColor, label: 'Confirmed' };
        case ORDER_STATUS.PROCESSING:
          return { color: styles.processing.backgroundColor, label: 'Processing' };
        case ORDER_STATUS.SHIPPED:
          return { color: styles.shipped.backgroundColor, label: 'Shipped' };
        case ORDER_STATUS.DELIVERED:
          return { color: styles.delivered.backgroundColor, label: 'Delivered' };
        case ORDER_STATUS.CANCELLED:
          return { color: styles.cancelled.backgroundColor, label: 'Cancelled' };
        case ORDER_STATUS.REFUNDED:
          return { color: styles.refunded.backgroundColor, label: 'Refunded' };
        default:
          return { color: styles.default.backgroundColor, label: status };
      }
    }

    return {
      color: customColor || styles.default.backgroundColor,
      label: status,
    };
  };

  const { color, label } = getStatusConfig();

  const badgeStyle = [
    styles.base,
    styles[size],
    { backgroundColor: color },
    style,
  ];

  const textStyle = [
    styles.text,
    styles[`${size}Text`],
  ];

  return (
    <View style={badgeStyle}>
      <Text style={textStyle}>{label}</Text>
    </View>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    base: {
      borderRadius: BORDER_RADIUS.round,
      alignItems: 'center',
      justifyContent: 'center',
      alignSelf: 'flex-start',
    },
    small: {
      paddingHorizontal: SPACING.xs,
      paddingVertical: SPACING.xs / 2,
    },
    medium: {
      paddingHorizontal: SPACING.sm,
      paddingVertical: SPACING.xs,
    },
    large: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
    },
    text: {
      color: colors.textOnPrimary,
      fontWeight: FONT_WEIGHTS.medium,
      textAlign: 'center',
    },
    smallText: {
      fontSize: FONT_SIZES.xs,
    },
    mediumText: {
      fontSize: FONT_SIZES.sm,
    },
    largeText: {
      fontSize: FONT_SIZES.md,
    },
    // Status colors
    approved: {
      backgroundColor: colors.success,
    },
    pending: {
      backgroundColor: colors.warning,
    },
    rejected: {
      backgroundColor: colors.error,
    },
    suspended: {
      backgroundColor: colors.error,
    },
    confirmed: {
      backgroundColor: colors.info,
    },
    processing: {
      backgroundColor: colors.warning,
    },
    shipped: {
      backgroundColor: colors.info,
    },
    delivered: {
      backgroundColor: colors.success,
    },
    cancelled: {
      backgroundColor: colors.error,
    },
    refunded: {
      backgroundColor: colors.error,
    },
    default: {
      backgroundColor: colors.textSecondary,
    },
  });
