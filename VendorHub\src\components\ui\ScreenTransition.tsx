import React, { useRef, useEffect } from 'react';
import {
  View,
  Animated,
  StyleSheet,
  Dimensions,
  Platform,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { useThemedStyles } from '../../hooks';
import { webCompatibleHaptics } from '../../utils/webCompatibility';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export interface ScreenTransitionProps {
  children: React.ReactNode;
  type?: 'fade' | 'slide' | 'scale' | 'flip' | 'bounce';
  direction?: 'up' | 'down' | 'left' | 'right';
  duration?: number;
  delay?: number;
  hapticFeedback?: boolean;
  style?: any;
}

export const ScreenTransition: React.FC<ScreenTransitionProps> = ({
  children,
  type = 'fade',
  direction = 'up',
  duration = 300,
  delay = 0,
  hapticFeedback = false,
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  const animatedValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (hapticFeedback) {
      webCompatibleHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    Animated.timing(animatedValue, {
      toValue: 1,
      duration,
      delay,
      useNativeDriver: true,
    }).start();
  }, [animatedValue, duration, delay, hapticFeedback]);

  const getAnimatedStyle = () => {
    switch (type) {
      case 'slide':
        return getSlideStyle();
      case 'scale':
        return getScaleStyle();
      case 'flip':
        return getFlipStyle();
      case 'bounce':
        return getBounceStyle();
      default:
        return getFadeStyle();
    }
  };

  const getFadeStyle = () => ({
    opacity: animatedValue,
  });

  const getSlideStyle = () => {
    const getTranslateValue = () => {
      switch (direction) {
        case 'up':
          return { translateY: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [50, 0],
          })};
        case 'down':
          return { translateY: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [-50, 0],
          })};
        case 'left':
          return { translateX: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [50, 0],
          })};
        case 'right':
          return { translateX: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [-50, 0],
          })};
        default:
          return { translateY: animatedValue.interpolate({
            inputRange: [0, 1],
            outputRange: [50, 0],
          })};
      }
    };

    return {
      opacity: animatedValue,
      transform: [getTranslateValue()],
    };
  };

  const getScaleStyle = () => ({
    opacity: animatedValue,
    transform: [
      {
        scale: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: [0.8, 1],
        }),
      },
    ],
  });

  const getFlipStyle = () => ({
    opacity: animatedValue,
    transform: [
      {
        rotateY: animatedValue.interpolate({
          inputRange: [0, 1],
          outputRange: ['90deg', '0deg'],
        }),
      },
    ],
  });

  const getBounceStyle = () => ({
    opacity: animatedValue,
    transform: [
      {
        scale: animatedValue.interpolate({
          inputRange: [0, 0.5, 1],
          outputRange: [0.3, 1.1, 1],
        }),
      },
    ],
  });

  return (
    <Animated.View style={[styles.container, getAnimatedStyle(), style]}>
      {children}
    </Animated.View>
  );
};

// Preset transition components
export const FadeInTransition: React.FC<Omit<ScreenTransitionProps, 'type'>> = (props) => (
  <ScreenTransition {...props} type="fade" />
);

export const SlideUpTransition: React.FC<Omit<ScreenTransitionProps, 'type' | 'direction'>> = (props) => (
  <ScreenTransition {...props} type="slide" direction="up" />
);

export const SlideDownTransition: React.FC<Omit<ScreenTransitionProps, 'type' | 'direction'>> = (props) => (
  <ScreenTransition {...props} type="slide" direction="down" />
);

export const ScaleInTransition: React.FC<Omit<ScreenTransitionProps, 'type'>> = (props) => (
  <ScreenTransition {...props} type="scale" />
);

export const BounceInTransition: React.FC<Omit<ScreenTransitionProps, 'type'>> = (props) => (
  <ScreenTransition {...props} type="bounce" />
);

// Staggered animation for lists
export const StaggeredTransition: React.FC<{
  children: React.ReactNode[];
  staggerDelay?: number;
  type?: ScreenTransitionProps['type'];
  direction?: ScreenTransitionProps['direction'];
}> = ({
  children,
  staggerDelay = 100,
  type = 'slide',
  direction = 'up',
}) => {
  return (
    <>
      {children.map((child, index) => (
        <ScreenTransition
          key={index}
          type={type}
          direction={direction}
          delay={index * staggerDelay}
          duration={300}
        >
          {child}
        </ScreenTransition>
      ))}
    </>
  );
};

// Page transition wrapper
export const PageTransition: React.FC<{
  children: React.ReactNode;
  isVisible: boolean;
  onTransitionComplete?: () => void;
}> = ({ children, isVisible, onTransitionComplete }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    if (isVisible) {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 300,
          friction: 10,
        }),
      ]).start(onTransitionComplete);
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.9,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible, fadeAnim, scaleAnim, onTransitionComplete]);

  if (!isVisible && fadeAnim._value === 0) {
    return null;
  }

  return (
    <Animated.View
      style={{
        flex: 1,
        opacity: fadeAnim,
        transform: [{ scale: scaleAnim }],
      }}
    >
      {children}
    </Animated.View>
  );
};

// Hook for managing screen transitions
export const useScreenTransition = (initialVisible = true) => {
  const [isVisible, setIsVisible] = React.useState(initialVisible);
  const [isTransitioning, setIsTransitioning] = React.useState(false);

  const show = React.useCallback(() => {
    setIsTransitioning(true);
    setIsVisible(true);
  }, []);

  const hide = React.useCallback(() => {
    setIsTransitioning(true);
    setIsVisible(false);
  }, []);

  const onTransitionComplete = React.useCallback(() => {
    setIsTransitioning(false);
  }, []);

  return {
    isVisible,
    isTransitioning,
    show,
    hide,
    onTransitionComplete,
  };
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
  },
});
