import React from 'react';
import { Image, Dimensions } from 'react-native';
import * as ImageManipulator from 'expo-image-manipulator';
import * as FileSystem from 'expo-file-system';
import { memoryManager } from './memoryManager';

const { width: screenWidth } = Dimensions.get('window');

export interface ImageOptimizationOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
  enableCaching?: boolean;
}

export interface OptimizedImage {
  uri: string;
  width: number;
  height: number;
  size: number;
  originalSize?: number;
}

export class ImageOptimizer {
  private static instance: ImageOptimizer;
  private cache = new Map<string, OptimizedImage>();
  private processingQueue = new Map<string, Promise<OptimizedImage>>();

  private constructor() {}

  public static getInstance(): ImageOptimizer {
    if (!ImageOptimizer.instance) {
      ImageOptimizer.instance = new ImageOptimizer();
    }
    return ImageOptimizer.instance;
  }

  /**
   * Optimize an image with the given options
   */
  public async optimizeImage(
    uri: string,
    options: ImageOptimizationOptions = {}
  ): Promise<OptimizedImage> {
    const cacheKey = this.getCacheKey(uri, options);
    
    // Check cache first
    if (options.enableCaching !== false && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    // Check if already processing
    if (this.processingQueue.has(cacheKey)) {
      return this.processingQueue.get(cacheKey)!;
    }

    // Start processing
    const processingPromise = this.processImage(uri, options);
    this.processingQueue.set(cacheKey, processingPromise);

    try {
      const result = await processingPromise;
      
      // Cache the result
      if (options.enableCaching !== false) {
        this.cache.set(cacheKey, result);
        memoryManager.cacheImage(result.uri, result.size);
      }
      
      return result;
    } finally {
      this.processingQueue.delete(cacheKey);
    }
  }

  /**
   * Process the image with the given options
   */
  private async processImage(
    uri: string,
    options: ImageOptimizationOptions
  ): Promise<OptimizedImage> {
    const {
      maxWidth = screenWidth * 2,
      maxHeight = screenWidth * 2,
      quality = 0.8,
      format = 'jpeg',
    } = options;

    try {
      // Get original image info
      const originalInfo = await this.getImageInfo(uri);
      const originalSize = await this.getFileSize(uri);

      // Calculate optimal dimensions
      const { width, height } = this.calculateOptimalDimensions(
        originalInfo.width,
        originalInfo.height,
        maxWidth,
        maxHeight
      );

      // Skip processing if image is already optimal
      if (
        originalInfo.width <= maxWidth &&
        originalInfo.height <= maxHeight &&
        originalSize < 1024 * 1024 // Less than 1MB
      ) {
        return {
          uri,
          width: originalInfo.width,
          height: originalInfo.height,
          size: originalSize,
          originalSize,
        };
      }

      // Process the image
      const result = await ImageManipulator.manipulateAsync(
        uri,
        [
          {
            resize: {
              width,
              height,
            },
          },
        ],
        {
          compress: quality,
          format: format === 'jpeg' ? ImageManipulator.SaveFormat.JPEG : 
                  format === 'png' ? ImageManipulator.SaveFormat.PNG :
                  ImageManipulator.SaveFormat.WEBP,
        }
      );

      const optimizedSize = await this.getFileSize(result.uri);

      return {
        uri: result.uri,
        width: result.width,
        height: result.height,
        size: optimizedSize,
        originalSize,
      };
    } catch (error) {
      console.error('Image optimization failed:', error);
      // Return original image info as fallback
      const originalInfo = await this.getImageInfo(uri);
      const originalSize = await this.getFileSize(uri);
      
      return {
        uri,
        width: originalInfo.width,
        height: originalInfo.height,
        size: originalSize,
        originalSize,
      };
    }
  }

  /**
   * Get image dimensions
   */
  private getImageInfo(uri: string): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      Image.getSize(
        uri,
        (width, height) => resolve({ width, height }),
        reject
      );
    });
  }

  /**
   * Get file size in bytes
   */
  private async getFileSize(uri: string): Promise<number> {
    try {
      const info = await FileSystem.getInfoAsync(uri);
      return info.exists ? info.size || 0 : 0;
    } catch {
      return 0;
    }
  }

  /**
   * Calculate optimal dimensions while maintaining aspect ratio
   */
  private calculateOptimalDimensions(
    originalWidth: number,
    originalHeight: number,
    maxWidth: number,
    maxHeight: number
  ): { width: number; height: number } {
    const aspectRatio = originalWidth / originalHeight;

    let width = originalWidth;
    let height = originalHeight;

    // Scale down if too wide
    if (width > maxWidth) {
      width = maxWidth;
      height = width / aspectRatio;
    }

    // Scale down if too tall
    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }

    return {
      width: Math.round(width),
      height: Math.round(height),
    };
  }

  /**
   * Generate cache key for the image and options
   */
  private getCacheKey(uri: string, options: ImageOptimizationOptions): string {
    const optionsStr = JSON.stringify(options);
    return `${uri}_${optionsStr}`;
  }

  /**
   * Clear the optimization cache
   */
  public clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): {
    cacheSize: number;
    processingQueueSize: number;
  } {
    return {
      cacheSize: this.cache.size,
      processingQueueSize: this.processingQueue.size,
    };
  }
}

// Utility functions for common image operations

/**
 * Optimize image for thumbnails
 */
export const optimizeForThumbnail = async (uri: string): Promise<OptimizedImage> => {
  const optimizer = ImageOptimizer.getInstance();
  return optimizer.optimizeImage(uri, {
    maxWidth: 200,
    maxHeight: 200,
    quality: 0.7,
    format: 'jpeg',
    enableCaching: true,
  });
};

/**
 * Optimize image for product display
 */
export const optimizeForProduct = async (uri: string): Promise<OptimizedImage> => {
  const optimizer = ImageOptimizer.getInstance();
  return optimizer.optimizeImage(uri, {
    maxWidth: screenWidth,
    maxHeight: screenWidth,
    quality: 0.8,
    format: 'jpeg',
    enableCaching: true,
  });
};

/**
 * Optimize image for full screen display
 */
export const optimizeForFullScreen = async (uri: string): Promise<OptimizedImage> => {
  const optimizer = ImageOptimizer.getInstance();
  return optimizer.optimizeImage(uri, {
    maxWidth: screenWidth * 2,
    maxHeight: screenWidth * 2,
    quality: 0.9,
    format: 'jpeg',
    enableCaching: true,
  });
};

/**
 * Batch optimize multiple images
 */
export const batchOptimizeImages = async (
  uris: string[],
  options: ImageOptimizationOptions = {},
  batchSize: number = 3
): Promise<OptimizedImage[]> => {
  const optimizer = ImageOptimizer.getInstance();
  const results: OptimizedImage[] = [];

  for (let i = 0; i < uris.length; i += batchSize) {
    const batch = uris.slice(i, i + batchSize);
    const batchResults = await Promise.all(
      batch.map(uri => optimizer.optimizeImage(uri, options))
    );
    results.push(...batchResults);

    // Add small delay between batches to prevent blocking
    if (i + batchSize < uris.length) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  return results;
};

/**
 * React hook for image optimization
 */
export const useImageOptimization = () => {
  const [isOptimizing, setIsOptimizing] = React.useState(false);
  const [optimizationProgress, setOptimizationProgress] = React.useState(0);

  const optimizeImage = React.useCallback(async (
    uri: string,
    options?: ImageOptimizationOptions
  ): Promise<OptimizedImage> => {
    setIsOptimizing(true);
    setOptimizationProgress(0);

    try {
      const optimizer = ImageOptimizer.getInstance();
      const result = await optimizer.optimizeImage(uri, options);
      setOptimizationProgress(100);
      return result;
    } finally {
      setIsOptimizing(false);
      setTimeout(() => setOptimizationProgress(0), 1000);
    }
  }, []);

  const optimizeMultiple = React.useCallback(async (
    uris: string[],
    options?: ImageOptimizationOptions
  ): Promise<OptimizedImage[]> => {
    setIsOptimizing(true);
    setOptimizationProgress(0);

    try {
      const results: OptimizedImage[] = [];
      for (let i = 0; i < uris.length; i++) {
        const result = await optimizeImage(uris[i], options);
        results.push(result);
        setOptimizationProgress(((i + 1) / uris.length) * 100);
      }
      return results;
    } finally {
      setIsOptimizing(false);
      setTimeout(() => setOptimizationProgress(0), 1000);
    }
  }, [optimizeImage]);

  return {
    optimizeImage,
    optimizeMultiple,
    isOptimizing,
    optimizationProgress,
  };
};

// Export singleton instance
export const imageOptimizer = ImageOptimizer.getInstance();
