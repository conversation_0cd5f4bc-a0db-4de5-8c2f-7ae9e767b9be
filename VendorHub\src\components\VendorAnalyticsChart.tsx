import React, { useState, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles } from '../hooks';
import { Card } from './Card';
import { Chart } from './charts/Chart';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../constants/theme';
import { formatCurrency } from '../utils';
import type { ThemeColors } from '../contexts/ThemeContext';
import type { Order, Product } from '../contexts/DataContext';

interface VendorAnalyticsChartProps {
  orders: Order[];
  products: Product[];
  style?: any;
}

interface AnalyticsData {
  revenue: Array<{ date: string; value: number }>;
  orders: Array<{ date: string; value: number }>;
  topProducts: Array<{ name: string; sales: number; revenue: number }>;
  categoryBreakdown: Array<{ category: string; count: number; percentage: number }>;
}

const { width: screenWidth } = Dimensions.get('window');

export const VendorAnalyticsChart: React.FC<VendorAnalyticsChartProps> = ({
  orders,
  products,
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [selectedChart, setSelectedChart] = useState<'revenue' | 'orders' | 'products'>('revenue');

  const periodOptions = [
    { key: '7d', label: '7 Days' },
    { key: '30d', label: '30 Days' },
    { key: '90d', label: '90 Days' },
    { key: '1y', label: '1 Year' },
  ];

  const chartOptions = [
    { key: 'revenue', label: 'Revenue', icon: 'trending-up-outline' },
    { key: 'orders', label: 'Orders', icon: 'receipt-outline' },
    { key: 'products', label: 'Products', icon: 'cube-outline' },
  ];

  const analyticsData = useMemo((): AnalyticsData => {
    const now = new Date();
    const daysMap = { '7d': 7, '30d': 30, '90d': 90, '1y': 365 };
    const days = daysMap[selectedPeriod];
    const startDate = new Date(now.getTime() - days * 24 * 60 * 60 * 1000);

    // Filter orders within the selected period
    const periodOrders = orders.filter(order => 
      new Date(order.createdAt) >= startDate
    );

    // Generate revenue data
    const revenueData = [];
    const orderCountData = [];
    const dateMap = new Map();

    for (let i = 0; i < days; i++) {
      const date = new Date(startDate.getTime() + i * 24 * 60 * 60 * 1000);
      const dateStr = date.toISOString().split('T')[0];
      dateMap.set(dateStr, { revenue: 0, orders: 0 });
    }

    periodOrders.forEach(order => {
      const dateStr = new Date(order.createdAt).toISOString().split('T')[0];
      if (dateMap.has(dateStr)) {
        const existing = dateMap.get(dateStr);
        dateMap.set(dateStr, {
          revenue: existing.revenue + order.totalAmount,
          orders: existing.orders + 1,
        });
      }
    });

    Array.from(dateMap.entries()).forEach(([date, data]) => {
      revenueData.push({ date, value: data.revenue });
      orderCountData.push({ date, value: data.orders });
    });

    // Calculate top products
    const productSales = new Map();
    periodOrders.forEach(order => {
      order.items.forEach(item => {
        const existing = productSales.get(item.productId) || { 
          name: item.productName, 
          sales: 0, 
          revenue: 0 
        };
        productSales.set(item.productId, {
          name: item.productName,
          sales: existing.sales + item.quantity,
          revenue: existing.revenue + item.totalPrice,
        });
      });
    });

    const topProducts = Array.from(productSales.values())
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // Calculate category breakdown
    const categoryMap = new Map();
    products.forEach(product => {
      const existing = categoryMap.get(product.category) || 0;
      categoryMap.set(product.category, existing + 1);
    });

    const totalProducts = products.length;
    const categoryBreakdown = Array.from(categoryMap.entries()).map(([category, count]) => ({
      category,
      count,
      percentage: totalProducts > 0 ? (count / totalProducts) * 100 : 0,
    }));

    return {
      revenue: revenueData,
      orders: orderCountData,
      topProducts,
      categoryBreakdown,
    };
  }, [orders, products, selectedPeriod]);

  const getCurrentChartData = () => {
    const rawData = (() => {
      switch (selectedChart) {
        case 'revenue':
          return analyticsData.revenue;
        case 'orders':
          return analyticsData.orders;
        default:
          return analyticsData.revenue;
      }
    })();

    // Safety check for empty data
    if (!rawData || rawData.length === 0) {
      return {
        labels: ['No Data'],
        datasets: [{
          data: [0],
          color: (opacity = 1) => `rgba(102, 126, 234, ${opacity})`,
          strokeWidth: 2,
        }],
      };
    }

    // Convert data to react-native-chart-kit format
    const labels = rawData.map(item => {
      const date = new Date(item.date);
      return selectedPeriod === '7d' || selectedPeriod === '30d'
        ? `${date.getMonth() + 1}/${date.getDate()}`
        : `${date.getMonth() + 1}/${date.getDate()}`;
    });

    const data = rawData.map(item => item.value || 0);

    // Ensure we have at least one data point
    const finalLabels = labels.length > 0 ? labels : ['No Data'];
    const finalData = data.length > 0 ? data : [0];

    return {
      labels: finalLabels.length > 10 ? finalLabels.filter((_, index) => index % Math.ceil(finalLabels.length / 10) === 0) : finalLabels,
      datasets: [{
        data: finalData.length > 10 ? finalData.filter((_, index) => index % Math.ceil(finalData.length / 10) === 0) : finalData,
        color: (opacity = 1) => `rgba(102, 126, 234, ${opacity})`,
        strokeWidth: 2,
      }],
    };
  };

  const getChartColor = () => {
    switch (selectedChart) {
      case 'revenue':
        return '#4CAF50';
      case 'orders':
        return '#2196F3';
      case 'products':
        return '#FF9800';
      default:
        return '#4CAF50';
    }
  };

  const formatChartValue = (value: number) => {
    if (selectedChart === 'revenue') {
      return formatCurrency(value);
    }
    return value.toString();
  };

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      {periodOptions.map((option) => (
        <TouchableOpacity
          key={option.key}
          style={[
            styles.periodOption,
            selectedPeriod === option.key && styles.periodOptionActive,
          ]}
          onPress={() => setSelectedPeriod(option.key as any)}
        >
          <Text
            style={[
              styles.periodOptionText,
              selectedPeriod === option.key && styles.periodOptionTextActive,
            ]}
          >
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderChartSelector = () => (
    <View style={styles.chartSelector}>
      {chartOptions.map((option) => (
        <TouchableOpacity
          key={option.key}
          style={[
            styles.chartOption,
            selectedChart === option.key && styles.chartOptionActive,
          ]}
          onPress={() => setSelectedChart(option.key as any)}
        >
          <Ionicons 
            name={option.icon as any} 
            size={20} 
            color={selectedChart === option.key ? '#667eea' : '#666'} 
          />
          <Text
            style={[
              styles.chartOptionText,
              selectedChart === option.key && styles.chartOptionTextActive,
            ]}
          >
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderTopProducts = () => (
    <View style={styles.topProductsSection}>
      <Text style={styles.sectionTitle}>Top Products ({selectedPeriod})</Text>
      <View style={styles.topProductsList}>
        {analyticsData.topProducts.map((product, index) => (
          <View key={index} style={styles.topProductItem}>
            <View style={styles.productRank}>
              <Text style={styles.rankText}>{index + 1}</Text>
            </View>
            <View style={styles.productInfo}>
              <Text style={styles.productName} numberOfLines={1}>
                {product.name}
              </Text>
              <Text style={styles.productStats}>
                {product.sales} sold • {formatCurrency(product.revenue)}
              </Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );

  const renderCategoryBreakdown = () => (
    <View style={styles.categorySection}>
      <Text style={styles.sectionTitle}>Product Categories</Text>
      <View style={styles.categoryList}>
        {analyticsData.categoryBreakdown.map((category, index) => (
          <View key={index} style={styles.categoryItem}>
            <View style={styles.categoryInfo}>
              <Text style={styles.categoryName}>{category.category}</Text>
              <Text style={styles.categoryCount}>{category.count} products</Text>
            </View>
            <View style={styles.categoryPercentage}>
              <Text style={styles.percentageText}>{category.percentage.toFixed(1)}%</Text>
            </View>
          </View>
        ))}
      </View>
    </View>
  );

  return (
    <View style={[styles.container, style]}>
      <Card style={styles.chartCard} variant="elevated">
        <View style={styles.chartHeader}>
          <Text style={styles.chartTitle}>Analytics Overview</Text>
          {renderPeriodSelector()}
        </View>

        {renderChartSelector()}

        <View style={styles.chartContainer}>
          <Chart
            data={getCurrentChartData()}
            type="line"
            height={200}
          />
        </View>
      </Card>

      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.insightsScroll}>
        <View style={styles.insightsContainer}>
          {renderTopProducts()}
          {renderCategoryBreakdown()}
        </View>
      </ScrollView>
    </View>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    gap: SPACING.lg,
  },
  chartCard: {
    padding: SPACING.lg,
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.lg,
  },
  chartTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderRadius: BORDER_RADIUS.md,
    padding: SPACING.xs,
  },
  periodOption: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    borderRadius: BORDER_RADIUS.sm,
  },
  periodOptionActive: {
    backgroundColor: '#667eea',
  },
  periodOptionText: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    fontWeight: FONT_WEIGHTS.medium,
  },
  periodOptionTextActive: {
    color: '#FFFFFF',
  },
  chartSelector: {
    flexDirection: 'row',
    marginBottom: SPACING.lg,
    gap: SPACING.sm,
  },
  chartOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    paddingHorizontal: SPACING.sm,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: colors.surface,
    gap: SPACING.xs,
  },
  chartOptionActive: {
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderWidth: 1,
    borderColor: '#667eea',
  },
  chartOptionText: {
    fontSize: FONT_SIZES.sm,
    color: colors.text,
    fontWeight: FONT_WEIGHTS.medium,
  },
  chartOptionTextActive: {
    color: '#667eea',
  },
  chartContainer: {
    height: 200,
    marginVertical: SPACING.md,
  },
  insightsScroll: {
    marginHorizontal: -SPACING.lg,
  },
  insightsContainer: {
    flexDirection: 'row',
    paddingHorizontal: SPACING.lg,
    gap: SPACING.lg,
  },
  topProductsSection: {
    width: screenWidth * 0.8,
  },
  categorySection: {
    width: screenWidth * 0.7,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
    marginBottom: SPACING.md,
  },
  topProductsList: {
    backgroundColor: colors.surface,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    gap: SPACING.md,
  },
  topProductItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
  },
  productRank: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
  },
  rankText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
    marginBottom: SPACING.xs,
  },
  productStats: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  categoryList: {
    backgroundColor: colors.surface,
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.md,
    gap: SPACING.md,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
    marginBottom: SPACING.xs,
  },
  categoryCount: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  categoryPercentage: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    backgroundColor: 'rgba(102, 126, 234, 0.1)',
    borderRadius: BORDER_RADIUS.sm,
  },
  percentageText: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#667eea',
  },
});
