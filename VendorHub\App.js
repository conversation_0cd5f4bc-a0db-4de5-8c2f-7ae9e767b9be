import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Platform } from 'react-native';
import 'react-native-reanimated';

// Import web polyfills and gesture handler configuration
import './src/utils/webPolyfills';
import './src/utils/gestureHandlerWeb';

import { AuthProvider } from './src/contexts/AuthContext';
import { DataProvider } from './src/contexts/DataContext';
import { ThemeProvider } from './src/contexts/ThemeContext';
import { OfflineIndicator } from './src/components';
import { AppNavigator } from './src/navigation';
import { StyleSheet } from 'react-native';
import I18nService from './src/services/I18nService';

// Initialize I18nService to set up RTL on app startup (already initialized by default export)

// Linking configuration for web deep linking
const linking = {
  prefixes: ['http://localhost:8081', 'https://vendorhub.app'],
  config: {
    screens: {
      // Auth screens
      Login: 'login',
      Register: 'register',

      // Vendor screens
      VendorTabs: {
        screens: {
          Overview: 'vendor/overview',
          Products: 'vendor/products',
          Orders: 'vendor/orders',
          Shop: 'vendor/shop',
        },
      },
      AddProduct: 'vendor/add-product',
      EditProduct: 'vendor/edit-product/:productId',
      ShopSettings: 'vendor/shop-settings',

      // Admin screens
      AdminTabs: {
        screens: {
          Dashboard: 'admin/dashboard',
          Vendors: 'admin/vendors',
          Products: 'admin/products',
          Orders: 'admin/orders',
        },
      },

      // Customer screens
      CustomerTabs: {
        screens: {
          Home: 'home',
          Shops: 'shops',
          Cart: 'cart',
          Profile: 'profile',
        },
      },
      ProductDetails: 'product/:productId',
      VendorShop: 'shop/:vendorId',
      Checkout: 'checkout',
    },
  },
};

export default function App() {
  const [loaded] = useFonts({
    SpaceMono: require('./assets/fonts/SpaceMono-Regular.ttf'),
  });

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <SafeAreaProvider>
        <ThemeProvider>
          <AuthProvider>
            <DataProvider>
              <NavigationContainer linking={Platform.OS === 'web' ? linking : undefined}>
                <AppNavigator />
              </NavigationContainer>
              <OfflineIndicator />
              <StatusBar style="auto" />
            </DataProvider>
          </AuthProvider>
        </ThemeProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
