import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

// Color palette based on requirements (#667eea to #764ba2 gradient)
export const COLORS = {
  // Primary gradient colors
  primary: '#667eea',
  primaryDark: '#764ba2',
  
  // Semantic colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  info: '#2196F3',
  
  // Status colors for vendors
  approved: '#4CAF50',
  pending: '#FF9800',
  rejected: '#F44336',
  
  // Neutral colors
  white: '#FFFFFF',
  black: '#000000',
  gray100: '#F5F5F5',
  gray200: '#EEEEEE',
  gray300: '#E0E0E0',
  gray400: '#BDBDBD',
  gray500: '#9E9E9E',
  gray600: '#757575',
  gray700: '#616161',
  gray800: '#424242',
  gray900: '#212121',
  
  // Background colors
  background: '#FFFFFF',
  backgroundSecondary: '#F8F9FA',
  surface: '#FFFFFF',
  
  // Text colors
  textPrimary: '#212121',
  textSecondary: '#757575',
  textDisabled: '#BDBDBD',
  textOnPrimary: '#FFFFFF',
  
  // Border colors
  border: '#E0E0E0',
  borderLight: '#F0F0F0',
  
  // Glassmorphism colors
  glassBackground: 'rgba(255, 255, 255, 0.25)',
  glassBorder: 'rgba(255, 255, 255, 0.18)',
  
  // Dark mode colors
  dark: {
    background: '#121212',
    backgroundSecondary: '#1E1E1E',
    surface: '#2D2D2D',
    textPrimary: '#FFFFFF',
    textSecondary: '#AAAAAA',
    border: '#333333',
  }
};

// Typography
export const FONTS = {
  regular: 'System',
  medium: 'System',
  bold: 'System',
  light: 'System',
};

export const FONT_SIZES = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
  display: 40,
};

export const FONT_WEIGHTS = {
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  bold: '700' as const,
};

// Spacing
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
  xxxl: 64,
};

// Border radius
export const BORDER_RADIUS = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  round: 50,
};

// Shadows
export const SHADOWS = {
  small: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  medium: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6.27,
    elevation: 10,
  },
  large: {
    shadowColor: COLORS.black,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.2,
    shadowRadius: 10.32,
    elevation: 16,
  },
};

// Screen dimensions
export const SCREEN = {
  width,
  height,
  isSmall: width < 375,
  isMedium: width >= 375 && width < 414,
  isLarge: width >= 414,
};

// Animation durations
export const ANIMATION = {
  fast: 200,
  medium: 300,
  slow: 500,
};

// Z-index values
export const Z_INDEX = {
  modal: 1000,
  overlay: 900,
  dropdown: 800,
  header: 700,
  fab: 600,
};

// Common gradients
export const GRADIENTS = {
  primary: ['#667eea', '#764ba2'],
  success: ['#4CAF50', '#45A049'],
  warning: ['#FF9800', '#F57C00'],
  error: ['#F44336', '#D32F2F'],
  glass: ['rgba(255, 255, 255, 0.25)', 'rgba(255, 255, 255, 0.1)'],
};

// Icon sizes
export const ICON_SIZES = {
  xs: 16,
  sm: 20,
  md: 24,
  lg: 32,
  xl: 40,
  xxl: 48,
};

// Component specific constants
export const HEADER_HEIGHT = 60;
export const TAB_BAR_HEIGHT = 80;
export const CARD_BORDER_RADIUS = BORDER_RADIUS.md;
export const BUTTON_HEIGHT = 48;
export const INPUT_HEIGHT = 48;

export default {
  COLORS,
  FONTS,
  FONT_SIZES,
  FONT_WEIGHTS,
  SPACING,
  BORDER_RADIUS,
  SHADOWS,
  SCREEN,
  ANIMATION,
  Z_INDEX,
  GRADIENTS,
  ICON_SIZES,
  HEADER_HEIGHT,
  TAB_BAR_HEIGHT,
  CARD_BORDER_RADIUS,
  BUTTON_HEIGHT,
  INPUT_HEIGHT,
};
