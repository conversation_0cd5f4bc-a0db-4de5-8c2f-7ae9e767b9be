import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useThemedStyles, useAuth, useProducts, useOrders } from '../hooks';
import { Card } from './Card';
import { StatusBadge } from './StatusBadge';
import RecommendationService, { ProductRecommendation, RecommendationContext } from '../services/RecommendationService';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../constants/theme';
import { formatCurrency } from '../utils';
import type { ThemeColors } from '../contexts/ThemeContext';
import type { Product } from '../contexts/DataContext';

interface RecommendationsSectionProps {
  title?: string;
  context?: RecommendationContext;
  limit?: number;
  style?: any;
  horizontal?: boolean;
  showReason?: boolean;
  onProductPress?: (product: Product) => void;
}

export const RecommendationsSection: React.FC<RecommendationsSectionProps> = ({
  title = 'Recommended for You',
  context,
  limit = 10,
  style,
  horizontal = true,
  showReason = false,
  onProductPress,
}) => {
  const styles = useThemedStyles(createStyles);
  const navigation = useNavigation();
  const { user } = useAuth();
  const { getAllProducts } = useProducts();
  const { getAllOrders } = useOrders();
  
  const [recommendations, setRecommendations] = useState<ProductRecommendation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadRecommendations();
  }, [user, context]);

  const loadRecommendations = async () => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const products = getAllProducts();
      const orders = getAllOrders();

      const recs = await RecommendationService.getRecommendations(
        user.id,
        products,
        orders,
        context,
        limit
      );

      setRecommendations(recs);
    } catch (err) {
      console.error('Error loading recommendations:', err);
      setError('Failed to load recommendations');
    } finally {
      setIsLoading(false);
    }
  };

  const handleProductPress = (product: Product) => {
    if (onProductPress) {
      onProductPress(product);
    } else {
      navigation.navigate('ProductDetails' as never, { productId: product.id } as never);
    }

    // Track the view
    if (user) {
      RecommendationService.trackProductView(
        user.id,
        product.id,
        product.category,
        product.vendorId
      );
    }
  };

  const getReasonText = (reason: ProductRecommendation['reason']) => {
    switch (reason) {
      case 'viewed_similar': return 'Similar to viewed items';
      case 'bought_together': return 'Frequently bought together';
      case 'trending': return 'Trending now';
      case 'category_match': return 'Based on your interests';
      case 'vendor_match': return 'From vendors you like';
      case 'price_range': return 'In your price range';
      case 'collaborative': return 'Others also liked';
      default: return 'Recommended';
    }
  };

  const getReasonIcon = (reason: ProductRecommendation['reason']) => {
    switch (reason) {
      case 'viewed_similar': return 'eye-outline';
      case 'bought_together': return 'bag-outline';
      case 'trending': return 'trending-up-outline';
      case 'category_match': return 'heart-outline';
      case 'vendor_match': return 'storefront-outline';
      case 'price_range': return 'pricetag-outline';
      case 'collaborative': return 'people-outline';
      default: return 'star-outline';
    }
  };

  const renderProductCard = (recommendation: ProductRecommendation, index: number) => {
    const { product, reason, confidence } = recommendation;
    
    return (
      <TouchableOpacity
        key={product.id}
        style={[
          styles.productCard,
          horizontal && styles.horizontalCard,
          !horizontal && index % 2 === 1 && styles.rightCard,
        ]}
        onPress={() => handleProductPress(product)}
      >
        <Card style={styles.cardContent} variant="elevated">
          <View style={styles.imageContainer}>
            <View style={styles.imagePlaceholder}>
              <Ionicons name="image-outline" size={32} color="#CCCCCC" />
            </View>
            
            {product.originalPrice && product.originalPrice > product.price && (
              <View style={styles.discountBadge}>
                <Text style={styles.discountText}>
                  {Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}% OFF
                </Text>
              </View>
            )}

            {showReason && (
              <View style={styles.reasonBadge}>
                <Ionicons 
                  name={getReasonIcon(reason) as any} 
                  size={12} 
                  color="#FFFFFF" 
                />
              </View>
            )}
          </View>

          <View style={styles.productInfo}>
            <Text style={styles.productName} numberOfLines={2}>
              {product.name}
            </Text>
            
            <View style={styles.priceContainer}>
              <Text style={styles.currentPrice}>
                {formatCurrency(product.price)}
              </Text>
              {product.originalPrice && product.originalPrice > product.price && (
                <Text style={styles.originalPrice}>
                  {formatCurrency(product.originalPrice)}
                </Text>
              )}
            </View>

            <View style={styles.productMeta}>
              <View style={styles.rating}>
                <Ionicons name="star" size={12} color="#FFD700" />
                <Text style={styles.ratingText}>{product.rating.toFixed(1)}</Text>
              </View>
              
              <StatusBadge
                status={product.inventory > 0 ? 'In Stock' : 'Out of Stock'}
                color={product.inventory > 0 ? '#4CAF50' : '#F44336'}
                size="small"
              />
            </View>

            {showReason && (
              <Text style={styles.reasonText} numberOfLines={1}>
                {getReasonText(reason)}
              </Text>
            )}
          </View>
        </Card>
      </TouchableOpacity>
    );
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color="#667eea" />
          <Text style={styles.loadingText}>Loading recommendations...</Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={styles.errorContainer}>
          <Ionicons name="alert-circle-outline" size={24} color="#F44336" />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity onPress={loadRecommendations} style={styles.retryButton}>
            <Text style={styles.retryText}>Retry</Text>
          </TouchableOpacity>
        </View>
      );
    }

    if (recommendations.length === 0) {
      return (
        <View style={styles.emptyContainer}>
          <Ionicons name="bulb-outline" size={24} color="#999" />
          <Text style={styles.emptyText}>No recommendations available</Text>
          <Text style={styles.emptySubtext}>
            Browse more products to get personalized recommendations
          </Text>
        </View>
      );
    }

    if (horizontal) {
      return (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.horizontalContainer}
        >
          {recommendations.map((rec, index) => renderProductCard(rec, index))}
        </ScrollView>
      );
    }

    return (
      <View style={styles.gridContainer}>
        {recommendations.map((rec, index) => renderProductCard(rec, index))}
      </View>
    );
  };

  if (!user) {
    return null;
  }

  return (
    <View style={[styles.container, style]}>
      <View style={styles.header}>
        <Text style={styles.title}>{title}</Text>
        {recommendations.length > 0 && (
          <TouchableOpacity onPress={loadRecommendations} style={styles.refreshButton}>
            <Ionicons name="refresh-outline" size={20} color="#667eea" />
          </TouchableOpacity>
        )}
      </View>
      
      {renderContent()}
    </View>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    marginVertical: SPACING.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.md,
  },
  title: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
  },
  refreshButton: {
    padding: SPACING.sm,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xl,
    gap: SPACING.sm,
  },
  loadingText: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  errorContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.sm,
  },
  errorText: {
    fontSize: FONT_SIZES.sm,
    color: '#F44336',
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: '#667eea',
    borderRadius: BORDER_RADIUS.md,
    marginTop: SPACING.sm,
  },
  retryText: {
    fontSize: FONT_SIZES.sm,
    color: '#FFFFFF',
    fontWeight: FONT_WEIGHTS.medium,
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: SPACING.xl,
    paddingHorizontal: SPACING.lg,
    gap: SPACING.sm,
  },
  emptyText: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
    fontWeight: FONT_WEIGHTS.medium,
  },
  emptySubtext: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  horizontalContainer: {
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: SPACING.lg,
    gap: SPACING.md,
  },
  productCard: {
    width: 160,
  },
  horizontalCard: {
    width: 160,
  },
  rightCard: {
    marginLeft: SPACING.md,
  },
  cardContent: {
    padding: SPACING.md,
  },
  imageContainer: {
    position: 'relative',
    marginBottom: SPACING.sm,
  },
  imagePlaceholder: {
    width: '100%',
    height: 120,
    backgroundColor: colors.surface,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  discountBadge: {
    position: 'absolute',
    top: SPACING.xs,
    left: SPACING.xs,
    backgroundColor: '#FF4444',
    paddingHorizontal: SPACING.xs,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.sm,
  },
  discountText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
  },
  reasonBadge: {
    position: 'absolute',
    top: SPACING.xs,
    right: SPACING.xs,
    backgroundColor: 'rgba(102, 126, 234, 0.9)',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productInfo: {
    gap: SPACING.xs,
  },
  productName: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
    lineHeight: 18,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  currentPrice: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: '#4CAF50',
  },
  originalPrice: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    textDecorationLine: 'line-through',
  },
  productMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  ratingText: {
    fontSize: FONT_SIZES.xs,
    color: colors.textSecondary,
  },
  reasonText: {
    fontSize: FONT_SIZES.xs,
    color: '#667eea',
    fontStyle: 'italic',
  },
});
