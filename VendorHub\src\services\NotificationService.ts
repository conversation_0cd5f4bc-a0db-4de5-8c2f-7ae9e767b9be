import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import { EventEmitter } from '../utils/EventEmitter';
import { storage } from '../utils/storage';

export interface NotificationData {
  id: string;
  title: string;
  body: string;
  data?: any;
  type: 'order' | 'vendor' | 'product' | 'system' | 'promotion' | 'chat' | 'reminder' | 'security';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  timestamp: string;
  read: boolean;
  userId: string;
  scheduledFor?: string;
  readAt?: string;
  actionUrl?: string;
  imageUrl?: string;
  sound?: string;
  vibration?: boolean;
  badge?: number;
}

export interface NotificationSettings {
  enabled: boolean;
  orderUpdates: boolean;
  vendorUpdates: boolean;
  promotions: boolean;
  systemAlerts: boolean;
  chatMessages: boolean;
  reminders: boolean;
  securityAlerts: boolean;
  sound: boolean;
  vibration: boolean;
  quietHours: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
  frequency: {
    promotional: 'immediate' | 'daily' | 'weekly' | 'never';
    orderUpdates: 'immediate' | 'batched';
  };
}

export interface NotificationTemplate {
  id: string;
  name: string;
  type: NotificationData['type'];
  title: string;
  body: string;
  variables: string[];
  defaultData?: Record<string, any>;
}

class NotificationService extends EventEmitter {
  private static instance: NotificationService;
  private expoPushToken: string | null = null;
  private notifications: NotificationData[] = [];
  private templates: NotificationTemplate[] = [];
  private settings: NotificationSettings = {
    enabled: true,
    orderUpdates: true,
    vendorUpdates: true,
    promotions: true,
    systemAlerts: true,
    chatMessages: true,
    reminders: true,
    securityAlerts: true,
    sound: true,
    vibration: true,
    quietHours: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00',
    },
    frequency: {
      promotional: 'weekly',
      orderUpdates: 'immediate',
    },
  };

  private constructor() {
    super();
    // Initialize synchronously first, then async operations
    this.initializeTemplatesSync();

    // Run async initialization in background
    this.initializeAsync().catch(error => {
      console.error('Error during NotificationService initialization:', error);
    });
  }

  private async initializeAsync() {
    try {
      await this.loadStoredData();
      await this.initializeNotifications();
    } catch (error) {
      console.error('Error in async initialization:', error);
    }
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }



  private async initializeNotifications() {
    try {
      // Configure notification behavior
      Notifications.setNotificationHandler({
        handleNotification: async () => ({
          shouldShowAlert: true,
          shouldPlaySound: this.settings.sound,
          shouldSetBadge: true,
        }),
      });

      // Request permissions and get push token
      await this.registerForPushNotificationsAsync();

      // Listen for notifications
      this.setupNotificationListeners();
    } catch (error) {
      console.error('Error initializing notifications:', error);
    }
  }

  private async registerForPushNotificationsAsync() {
    if (!Device.isDevice) {
      console.log('Must use physical device for Push Notifications');
      return;
    }

    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.log('Failed to get push token for push notification!');
      return;
    }

    try {
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      });
      this.expoPushToken = token.data;
      console.log('Expo Push Token:', this.expoPushToken);
    } catch (error) {
      console.log('Error getting push token:', error);
    }

    if (Platform.OS === 'android') {
      Notifications.setNotificationChannelAsync('default', {
        name: 'default',
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
      });
    }
  }

  private setupNotificationListeners() {
    // Handle notification received while app is in foreground
    Notifications.addNotificationReceivedListener((notification) => {
      this.handleNotificationReceived(notification);
    });

    // Handle notification tapped
    Notifications.addNotificationResponseReceivedListener((response) => {
      this.handleNotificationResponse(response);
    });
  }

  private handleNotificationReceived(notification: Notifications.Notification) {
    const notificationData: NotificationData = {
      id: notification.request.identifier,
      title: notification.request.content.title || '',
      body: notification.request.content.body || '',
      data: notification.request.content.data,
      type: notification.request.content.data?.type || 'system',
      priority: notification.request.content.data?.priority || 'normal',
      timestamp: new Date().toISOString(),
      read: false,
      userId: notification.request.content.data?.userId || 'unknown',
    };

    this.addNotification(notificationData);
  }

  private handleNotificationResponse(response: Notifications.NotificationResponse) {
    const notificationData = response.notification.request.content.data;
    
    // Handle different notification types
    switch (notificationData?.type) {
      case 'order':
        // Navigate to order details
        break;
      case 'vendor':
        // Navigate to vendor updates
        break;
      case 'product':
        // Navigate to product details
        break;
      default:
        // Default action
        break;
    }
  }

  // Public methods
  async scheduleLocalNotification(
    title: string,
    body: string,
    data?: any,
    trigger?: Notifications.NotificationTriggerInput
  ) {
    if (!this.settings.enabled) return;

    try {
      const identifier = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: this.settings.sound,
        },
        trigger: trigger || null,
      });

      return identifier;
    } catch (error) {
      console.log('Error scheduling notification:', error);
    }
  }

  async cancelNotification(identifier: string) {
    try {
      await Notifications.cancelScheduledNotificationAsync(identifier);
    } catch (error) {
      console.log('Error canceling notification:', error);
    }
  }

  async cancelAllNotifications() {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.log('Error canceling all notifications:', error);
    }
  }

  addNotification(notification: NotificationData) {
    this.notifications.unshift(notification);
    // Keep only last 100 notifications
    if (this.notifications.length > 100) {
      this.notifications = this.notifications.slice(0, 100);
    }
  }

  getNotifications(): NotificationData[] {
    return this.notifications;
  }



  deleteNotification(notificationId: string) {
    this.notifications = this.notifications.filter(n => n.id !== notificationId);
  }

  clearAllNotifications() {
    this.notifications = [];
  }

  getSettings(): NotificationSettings {
    return { ...this.settings };
  }

  updateSettings(newSettings: Partial<NotificationSettings>) {
    this.settings = { ...this.settings, ...newSettings };
  }

  getPushToken(): string | null {
    return this.expoPushToken;
  }

  // Predefined notification templates
  async sendOrderUpdateNotification(orderId: string, status: string) {
    if (!this.settings.orderUpdates) return;

    const title = 'Order Update';
    const body = `Your order #${orderId} is now ${status}`;
    
    await this.scheduleLocalNotification(title, body, {
      type: 'order',
      orderId,
      status,
    });
  }

  async sendVendorApprovalNotification(vendorName: string, approved: boolean) {
    if (!this.settings.vendorUpdates) return;

    const title = approved ? 'Vendor Approved!' : 'Vendor Application Update';
    const body = approved 
      ? `${vendorName} has been approved and can now start selling`
      : `${vendorName}'s application needs attention`;
    
    await this.scheduleLocalNotification(title, body, {
      type: 'vendor',
      vendorName,
      approved,
    });
  }

  async sendPromotionNotification(title: string, description: string, productId?: string) {
    if (!this.settings.promotions) return;

    await this.scheduleLocalNotification(title, description, {
      type: 'promotion',
      productId,
    });
  }

  // Advanced Notification Methods
  async sendFromTemplate(
    templateId: string,
    userId: string,
    variables: Record<string, string>,
    overrides: Partial<NotificationData> = {}
  ): Promise<string> {
    const template = this.templates.find(t => t.id === templateId);
    if (!template) {
      throw new Error(`Template ${templateId} not found`);
    }

    const title = this.replaceVariables(template.title, variables);
    const body = this.replaceVariables(template.body, variables);

    const notification: NotificationData = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      title,
      body,
      type: template.type,
      priority: 'normal',
      timestamp: new Date().toISOString(),
      read: false,
      userId,
      data: { ...template.defaultData, ...variables },
      ...overrides,
    };

    return this.sendAdvancedNotification(notification);
  }

  async sendAdvancedNotification(notification: NotificationData): Promise<string> {
    // Check if notification should be sent based on settings
    if (!this.shouldSendNotification(notification)) {
      return notification.id;
    }

    // Store notification
    this.notifications.push(notification);
    this.emit('notificationCreated', notification);

    // Send push notification
    await this.scheduleLocalNotification(notification.title, notification.body, notification.data);

    await this.saveData();
    return notification.id;
  }

  async scheduleNotification(
    notification: Omit<NotificationData, 'id' | 'timestamp'>,
    scheduledFor: Date
  ): Promise<string> {
    const notificationData: NotificationData = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString(),
      scheduledFor: scheduledFor.toISOString(),
    };

    // Store notification
    this.notifications.push(notificationData);

    // Schedule push notification
    await Notifications.scheduleNotificationAsync({
      content: {
        title: notificationData.title,
        body: notificationData.body,
        data: notificationData.data,
        sound: notificationData.sound || 'default',
        badge: notificationData.badge,
      },
      trigger: { date: scheduledFor },
    });

    await this.saveData();
    return notificationData.id;
  }

  async markAsRead(notificationId: string, userId: string): Promise<void> {
    const notification = this.notifications.find(n => n.id === notificationId && n.userId === userId);
    if (notification && !notification.readAt) {
      notification.read = true;
      notification.readAt = new Date().toISOString();
      await this.saveData();
      this.emit('notificationRead', notification);
    }
  }

  async markAllAsRead(userId: string): Promise<void> {
    const userNotifications = this.notifications.filter(n => n.userId === userId && !n.read);
    const now = new Date().toISOString();

    userNotifications.forEach(notification => {
      notification.read = true;
      notification.readAt = now;
    });

    await this.saveData();
    this.emit('allNotificationsRead', { userId, count: userNotifications.length });
  }

  getUserNotifications(
    userId: string,
    options: {
      unreadOnly?: boolean;
      type?: NotificationData['type'];
      limit?: number;
      offset?: number;
    } = {}
  ): NotificationData[] {
    let userNotifications = this.notifications.filter(n => n.userId === userId);

    if (options.unreadOnly) {
      userNotifications = userNotifications.filter(n => !n.read);
    }

    if (options.type) {
      userNotifications = userNotifications.filter(n => n.type === options.type);
    }

    // Sort by timestamp (newest first)
    userNotifications.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    // Apply pagination
    const offset = options.offset || 0;
    const limit = options.limit || userNotifications.length;

    return userNotifications.slice(offset, offset + limit);
  }

  getUnreadCount(userId: string): number {
    try {
      if (!userId) {
        return 0;
      }
      return this.notifications.filter(n => n.userId === userId && !n.read).length;
    } catch (error) {
      console.error('Error getting unread count:', error);
      return 0;
    }
  }

  // Helper Methods
  private shouldSendNotification(notification: NotificationData): boolean {
    // Check quiet hours
    if (this.settings.quietHours.enabled) {
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

      if (this.isInQuietHours(currentTime, this.settings.quietHours.startTime, this.settings.quietHours.endTime)) {
        return notification.priority === 'urgent';
      }
    }

    // Check type-specific settings
    switch (notification.type) {
      case 'order':
        return this.settings.orderUpdates;
      case 'vendor':
        return this.settings.vendorUpdates;
      case 'promotion':
        return this.settings.promotions;
      case 'system':
        return this.settings.systemAlerts;
      case 'chat':
        return this.settings.chatMessages;
      case 'reminder':
        return this.settings.reminders;
      case 'security':
        return this.settings.securityAlerts;
      default:
        return true;
    }
  }

  private isInQuietHours(currentTime: string, startTime: string, endTime: string): boolean {
    const current = this.timeToMinutes(currentTime);
    const start = this.timeToMinutes(startTime);
    const end = this.timeToMinutes(endTime);

    if (start <= end) {
      return current >= start && current <= end;
    } else {
      // Quiet hours span midnight
      return current >= start || current <= end;
    }
  }

  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  private replaceVariables(template: string, variables: Record<string, string>): string {
    let result = template;
    Object.entries(variables).forEach(([key, value]) => {
      result = result.replace(new RegExp(`{{${key}}}`, 'g'), value);
    });
    return result;
  }

  private initializeTemplatesSync(): void {
    this.templates = [
      {
        id: 'order_confirmed',
        name: 'Order Confirmed',
        type: 'order',
        title: 'Order Confirmed!',
        body: 'Your order #{{orderNumber}} has been confirmed and is being processed.',
        variables: ['orderNumber'],
      },
      {
        id: 'order_shipped',
        name: 'Order Shipped',
        type: 'order',
        title: 'Order Shipped!',
        body: 'Your order #{{orderNumber}} has been shipped. Track it with {{trackingNumber}}.',
        variables: ['orderNumber', 'trackingNumber'],
      },
      {
        id: 'new_message',
        name: 'New Message',
        type: 'chat',
        title: 'New message from {{senderName}}',
        body: '{{messagePreview}}',
        variables: ['senderName', 'messagePreview'],
      },
      {
        id: 'price_drop',
        name: 'Price Drop Alert',
        type: 'promotion',
        title: 'Price Drop Alert!',
        body: '{{productName}} is now {{newPrice}} (was {{oldPrice}})',
        variables: ['productName', 'newPrice', 'oldPrice'],
      },
      {
        id: 'security_alert',
        name: 'Security Alert',
        type: 'security',
        title: 'Security Alert',
        body: 'Unusual activity detected on your account. Please review your recent activity.',
        variables: [],
      },
    ];
  }

  // Storage Methods
  private async loadStoredData(): Promise<void> {
    try {
      const [notificationsData, settingsData] = await Promise.all([
        storage.getItem('notifications'),
        storage.getItem('notificationSettings'),
      ]);

      if (notificationsData) {
        this.notifications = JSON.parse(notificationsData);
      }

      if (settingsData) {
        this.settings = { ...this.settings, ...JSON.parse(settingsData) };
      }
    } catch (error) {
      console.error('Error loading notification data:', error);
    }
  }

  private async saveData(): Promise<void> {
    try {
      await Promise.all([
        storage.setItem('notifications', JSON.stringify(this.notifications)),
        storage.setItem('notificationSettings', JSON.stringify(this.settings)),
      ]);
    } catch (error) {
      console.error('Error saving notification data:', error);
    }
  }

  // Public API
  getTemplates(): NotificationTemplate[] {
    return [...this.templates];
  }

  async updateSettings(newSettings: Partial<NotificationSettings>): Promise<void> {
    this.settings = { ...this.settings, ...newSettings };
    await this.saveData();
    this.emit('settingsUpdated', this.settings);
  }

  async sendSystemAlert(title: string, message: string, priority: 'low' | 'normal' | 'high' = 'normal') {
    if (!this.settings.systemAlerts) return;

    await this.scheduleLocalNotification(title, message, {
      type: 'system',
      priority,
    });
  }

  // Simulate notifications for demo purposes
  simulateNotifications() {
    const demoNotifications: Omit<NotificationData, 'id' | 'timestamp' | 'read'>[] = [
      {
        title: 'Order Shipped',
        body: 'Your order #12345 has been shipped and is on its way!',
        type: 'order',
        priority: 'normal',
        data: { orderId: '12345', status: 'shipped' },
      },
      {
        title: 'New Vendor Approved',
        body: 'TechGear Pro has been approved and added to the marketplace',
        type: 'vendor',
        priority: 'normal',
        data: { vendorId: 'vendor-123' },
      },
      {
        title: 'Flash Sale!',
        body: '50% off on all electronics - Limited time offer',
        type: 'promotion',
        priority: 'high',
        data: { category: 'Electronics' },
      },
      {
        title: 'System Maintenance',
        body: 'Scheduled maintenance tonight from 2-4 AM',
        type: 'system',
        priority: 'low',
        data: { maintenanceWindow: '2-4 AM' },
      },
    ];

    demoNotifications.forEach((notification, index) => {
      setTimeout(() => {
        this.addNotification({
          ...notification,
          id: `demo-${Date.now()}-${index}`,
          timestamp: new Date().toISOString(),
          read: false,
          userId: 'demo-user', // Default userId for demo notifications
        });
      }, index * 2000); // Stagger notifications
    });
  }
}

// Create and export the singleton instance
let notificationServiceInstance: NotificationService | null = null;

try {
  notificationServiceInstance = NotificationService.getInstance();
} catch (error) {
  console.error('Error creating NotificationService instance:', error);
}

// Export with fallback
export const notificationService = notificationServiceInstance;
export default notificationServiceInstance;
