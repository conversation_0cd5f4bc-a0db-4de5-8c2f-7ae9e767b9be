import { useMemo } from 'react';
import { useData } from '../contexts/DataContext';
import { ORDER_STATUS } from '../constants';
import type { Order, OrderStatus } from '../contexts/DataContext';

export const useOrders = () => {
  const {
    orders,
    createOrder,
    updateOrderStatus,
    getOrderById,
    getCustomerOrders,
    getVendorOrders,
    isLoading,
    error,
  } = useData();

  // Memoized order statistics
  const orderStats = useMemo(() => {
    const total = orders.length;
    const pending = orders.filter(o => o.status === ORDER_STATUS.PENDING).length;
    const confirmed = orders.filter(o => o.status === ORDER_STATUS.CONFIRMED).length;
    const processing = orders.filter(o => o.status === ORDER_STATUS.PROCESSING).length;
    const shipped = orders.filter(o => o.status === ORDER_STATUS.SHIPPED).length;
    const delivered = orders.filter(o => o.status === ORDER_STATUS.DELIVERED).length;
    const cancelled = orders.filter(o => o.status === ORDER_STATUS.CANCELLED).length;
    const refunded = orders.filter(o => o.status === ORDER_STATUS.REFUNDED).length;

    const totalRevenue = orders
      .filter(o => o.status !== ORDER_STATUS.CANCELLED && o.status !== ORDER_STATUS.REFUNDED)
      .reduce((sum, order) => sum + order.totalAmount, 0);

    const averageOrderValue = total > 0 ? totalRevenue / total : 0;

    const completionRate = total > 0 ? (delivered / total) * 100 : 0;
    const cancellationRate = total > 0 ? (cancelled / total) * 100 : 0;

    return {
      total,
      pending,
      confirmed,
      processing,
      shipped,
      delivered,
      cancelled,
      refunded,
      totalRevenue,
      averageOrderValue,
      completionRate,
      cancellationRate,
    };
  }, [orders]);

  // Get orders by status
  const getOrdersByStatus = (status: OrderStatus): Order[] => {
    return orders.filter(order => order.status === status);
  };

  // Get recent orders (last 30 days)
  const getRecentOrders = (days: number = 30): Order[] => {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    return orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= cutoffDate;
    });
  };

  // Get orders by date range
  const getOrdersByDateRange = (startDate: Date, endDate: Date): Order[] => {
    return orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= startDate && orderDate <= endDate;
    });
  };

  // Get top customers by order count
  const getTopCustomersByOrderCount = (limit: number = 10) => {
    const customerOrderCounts = orders.reduce((acc, order) => {
      const customerId = order.customerId;
      if (!acc[customerId]) {
        acc[customerId] = {
          customerId,
          customerName: order.customerName,
          customerEmail: order.customerEmail,
          orderCount: 0,
          totalSpent: 0,
        };
      }
      acc[customerId].orderCount++;
      acc[customerId].totalSpent += order.totalAmount;
      return acc;
    }, {} as Record<string, any>);

    return Object.values(customerOrderCounts)
      .sort((a: any, b: any) => b.orderCount - a.orderCount)
      .slice(0, limit);
  };

  // Get top customers by total spent
  const getTopCustomersBySpending = (limit: number = 10) => {
    const customerOrderCounts = orders.reduce((acc, order) => {
      const customerId = order.customerId;
      if (!acc[customerId]) {
        acc[customerId] = {
          customerId,
          customerName: order.customerName,
          customerEmail: order.customerEmail,
          orderCount: 0,
          totalSpent: 0,
        };
      }
      acc[customerId].orderCount++;
      acc[customerId].totalSpent += order.totalAmount;
      return acc;
    }, {} as Record<string, any>);

    return Object.values(customerOrderCounts)
      .sort((a: any, b: any) => b.totalSpent - a.totalSpent)
      .slice(0, limit);
  };

  // Get revenue by period
  const getRevenueByPeriod = (period: 'day' | 'week' | 'month' | 'year') => {
    const revenueData: Record<string, number> = {};

    orders
      .filter(o => o.status !== ORDER_STATUS.CANCELLED && o.status !== ORDER_STATUS.REFUNDED)
      .forEach(order => {
        const date = new Date(order.createdAt);
        let key: string;

        switch (period) {
          case 'day':
            key = date.toISOString().split('T')[0];
            break;
          case 'week':
            const weekStart = new Date(date);
            weekStart.setDate(date.getDate() - date.getDay());
            key = weekStart.toISOString().split('T')[0];
            break;
          case 'month':
            key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            break;
          case 'year':
            key = String(date.getFullYear());
            break;
          default:
            key = date.toISOString().split('T')[0];
        }

        revenueData[key] = (revenueData[key] || 0) + order.totalAmount;
      });

    return Object.entries(revenueData)
      .map(([period, revenue]) => ({ period, revenue }))
      .sort((a, b) => a.period.localeCompare(b.period));
  };

  // Search orders
  const searchOrders = (query: string): Order[] => {
    const lowercaseQuery = query.toLowerCase();
    return orders.filter(order =>
      order.id.toLowerCase().includes(lowercaseQuery) ||
      order.customerName.toLowerCase().includes(lowercaseQuery) ||
      order.customerEmail.toLowerCase().includes(lowercaseQuery) ||
      order.trackingNumber?.toLowerCase().includes(lowercaseQuery) ||
      order.items.some(item => 
        item.productName.toLowerCase().includes(lowercaseQuery)
      )
    );
  };

  // Get orders requiring attention (pending, processing)
  const getOrdersRequiringAttention = (): Order[] => {
    return orders.filter(order => 
      order.status === ORDER_STATUS.PENDING || 
      order.status === ORDER_STATUS.PROCESSING
    );
  };

  // Get overdue orders (processing for more than 3 days)
  const getOverdueOrders = (): Order[] => {
    const threeDaysAgo = new Date();
    threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);

    return orders.filter(order => {
      const orderDate = new Date(order.updatedAt);
      return order.status === ORDER_STATUS.PROCESSING && orderDate < threeDaysAgo;
    });
  };

  // Bulk update order status
  const bulkUpdateOrderStatus = async (orderIds: string[], status: OrderStatus): Promise<void> => {
    const promises = orderIds.map(id => updateOrderStatus(id, status));
    await Promise.all(promises);
  };

  // Get order fulfillment metrics
  const getOrderFulfillmentMetrics = () => {
    const deliveredOrders = orders.filter(o => o.status === ORDER_STATUS.DELIVERED);
    
    if (deliveredOrders.length === 0) {
      return {
        averageFulfillmentTime: 0,
        fastestFulfillment: 0,
        slowestFulfillment: 0,
      };
    }

    const fulfillmentTimes = deliveredOrders
      .filter(order => order.deliveredAt)
      .map(order => {
        const orderDate = new Date(order.createdAt);
        const deliveryDate = new Date(order.deliveredAt!);
        return deliveryDate.getTime() - orderDate.getTime();
      });

    const averageFulfillmentTime = fulfillmentTimes.reduce((sum, time) => sum + time, 0) / fulfillmentTimes.length;
    const fastestFulfillment = Math.min(...fulfillmentTimes);
    const slowestFulfillment = Math.max(...fulfillmentTimes);

    // Convert from milliseconds to days
    return {
      averageFulfillmentTime: averageFulfillmentTime / (1000 * 60 * 60 * 24),
      fastestFulfillment: fastestFulfillment / (1000 * 60 * 60 * 24),
      slowestFulfillment: slowestFulfillment / (1000 * 60 * 60 * 24),
    };
  };

  // Sort orders
  const sortOrders = (
    orderList: Order[],
    sortBy: 'createdAt' | 'totalAmount' | 'status' | 'customerName',
    order: 'asc' | 'desc' = 'desc'
  ): Order[] => {
    return [...orderList].sort((a, b) => {
      let aValue: any = a[sortBy];
      let bValue: any = b[sortBy];

      if (sortBy === 'createdAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (aValue < bValue) return order === 'asc' ? -1 : 1;
      if (aValue > bValue) return order === 'asc' ? 1 : -1;
      return 0;
    });
  };

  // Additional helper methods
  const getAllOrders = () => orders;

  const getOrdersByVendor = (vendorId: string) => {
    return orders.filter(order =>
      order.items.some(item => item.vendorId === vendorId)
    );
  };

  return {
    // Data
    orders,
    orderStats,
    isLoading,
    error,

    // Methods
    createOrder,
    updateOrderStatus,
    bulkUpdateOrderStatus,
    getOrderById,
    getOrdersByStatus,
    getCustomerOrders,
    getVendorOrders,
    getAllOrders,
    getOrdersByVendor,
    getRecentOrders,
    getOrdersByDateRange,
    getTopCustomersByOrderCount,
    getTopCustomersBySpending,
    getRevenueByPeriod,
    searchOrders,
    getOrdersRequiringAttention,
    getOverdueOrders,
    getOrderFulfillmentMetrics,
    sortOrders,
  };
};
