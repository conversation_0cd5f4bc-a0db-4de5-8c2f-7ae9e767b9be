import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS, BORDER_RADIUS } from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';

export interface StatisticsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: keyof typeof Ionicons.glyphMap;
  trend?: {
    value: number;
    isPositive: boolean;
    period: string;
  };
  color?: string;
  gradient?: string[];
  onPress?: () => void;
  style?: any;
  format?: 'currency' | 'number' | 'percentage' | 'text';
}

export const StatisticsCard: React.FC<StatisticsCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  color = '#667eea',
  gradient,
  onPress,
  style,
  format = 'text',
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const formatValue = (val: string | number): string => {
    if (typeof val === 'string') return val;
    
    switch (format) {
      case 'currency':
        return formatCurrency(val);
      case 'number':
        return val.toLocaleString();
      case 'percentage':
        return `${val.toFixed(1)}%`;
      default:
        return val.toString();
    }
  };

  const renderContent = () => (
    <View style={styles.content}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{title}</Text>
          {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
        </View>
        {icon && (
          <View style={[styles.iconContainer, { backgroundColor: color + '20' }]}>
            <Ionicons name={icon} size={24} color={color} />
          </View>
        )}
      </View>

      {/* Value */}
      <View style={styles.valueContainer}>
        <Text style={[styles.value, { color }]}>
          {formatValue(value)}
        </Text>
        
        {trend && (
          <View style={styles.trendContainer}>
            <View style={[
              styles.trendBadge,
              { backgroundColor: trend.isPositive ? '#4CAF50' : '#FF6B6B' }
            ]}>
              <Ionicons
                name={trend.isPositive ? 'trending-up' : 'trending-down'}
                size={12}
                color="#FFFFFF"
              />
              <Text style={styles.trendValue}>
                {Math.abs(trend.value).toFixed(1)}%
              </Text>
            </View>
            <Text style={styles.trendPeriod}>{trend.period}</Text>
          </View>
        )}
      </View>
    </View>
  );

  if (gradient) {
    return (
      <TouchableOpacity
        style={[styles.container, style]}
        onPress={onPress}
        disabled={!onPress}
        activeOpacity={onPress ? 0.8 : 1}
      >
        <LinearGradient
          colors={gradient}
          style={styles.gradientContainer}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          {renderContent()}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[styles.container, styles.regularContainer, style]}
      onPress={onPress}
      disabled={!onPress}
      activeOpacity={onPress ? 0.8 : 1}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      borderRadius: BORDER_RADIUS.md,
      overflow: 'hidden',
      elevation: 3,
      shadowColor: '#000',
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.22,
      shadowRadius: 2.22,
    },
    regularContainer: {
      backgroundColor: colors.surface,
    },
    gradientContainer: {
      padding: SPACING.md,
    },
    content: {
      padding: SPACING.md,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
      marginBottom: SPACING.sm,
    },
    titleContainer: {
      flex: 1,
      marginRight: SPACING.sm,
    },
    title: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textSecondary,
      marginBottom: SPACING.xs,
    },
    subtitle: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      opacity: 0.8,
    },
    iconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      justifyContent: 'center',
      alignItems: 'center',
    },
    valueContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
    },
    value: {
      fontSize: FONT_SIZES.xxl,
      fontWeight: FONT_WEIGHTS.bold,
      flex: 1,
    },
    trendContainer: {
      alignItems: 'flex-end',
    },
    trendBadge: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: SPACING.xs,
      paddingVertical: 2,
      borderRadius: BORDER_RADIUS.sm,
      marginBottom: SPACING.xs,
    },
    trendValue: {
      fontSize: FONT_SIZES.xs,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#FFFFFF',
      marginLeft: 2,
    },
    trendPeriod: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      opacity: 0.8,
    },
  });

export default StatisticsCard;
