import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles, useAuth } from '../../hooks';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
} from '../../constants/theme';
import { notificationService } from '../../services/NotificationService';
import type { ThemeColors } from '../../contexts/ThemeContext';

export interface NotificationBadgeProps {
  onPress?: () => void;
  size?: 'small' | 'medium' | 'large';
  showCount?: boolean;
  style?: any;
}

export const NotificationBadge: React.FC<NotificationBadgeProps> = ({
  onPress,
  size = 'medium',
  showCount = true,
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const [unreadCount, setUnreadCount] = useState(0);
  const [animatedValue] = useState(new Animated.Value(1));

  useEffect(() => {
    // Initial load
    updateUnreadCount();

    // Set up interval to check for new notifications
    const interval = setInterval(updateUnreadCount, 1000);

    return () => clearInterval(interval);
  }, [user?.id]); // Re-run when user changes

  useEffect(() => {
    if (unreadCount > 0) {
      // Animate badge when new notifications arrive
      Animated.sequence([
        Animated.timing(animatedValue, {
          toValue: 1.2,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [unreadCount]);

  const updateUnreadCount = () => {
    try {
      if (!user?.id) {
        setUnreadCount(0);
        return;
      }

      // Defensive check for notificationService
      if (!notificationService || typeof notificationService.getUnreadCount !== 'function') {
        console.warn('NotificationService not available or getUnreadCount method missing');
        setUnreadCount(0);
        return;
      }

      const count = notificationService.getUnreadCount(user.id);
      setUnreadCount(count);
    } catch (error) {
      console.error('Error updating unread count:', error);
      setUnreadCount(0);
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'small':
        return 20;
      case 'large':
        return 28;
      default:
        return 24;
    }
  };

  const getBadgeSize = () => {
    switch (size) {
      case 'small':
        return { width: 16, height: 16, borderRadius: 8 };
      case 'large':
        return { width: 24, height: 24, borderRadius: 12 };
      default:
        return { width: 20, height: 20, borderRadius: 10 };
    }
  };

  const getBadgeTextSize = () => {
    switch (size) {
      case 'small':
        return FONT_SIZES.xs;
      case 'large':
        return FONT_SIZES.sm;
      default:
        return FONT_SIZES.xs;
    }
  };

  const formatCount = (count: number): string => {
    if (count > 99) return '99+';
    return count.toString();
  };

  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <Animated.View
        style={[
          styles.iconContainer,
          { transform: [{ scale: animatedValue }] },
        ]}
      >
        <Ionicons
          name="notifications-outline"
          size={getIconSize()}
          color="#FFFFFF"
        />
        
        {unreadCount > 0 && (
          <View style={[styles.badge, getBadgeSize()]}>
            {showCount ? (
              <Text style={[styles.badgeText, { fontSize: getBadgeTextSize() }]}>
                {formatCount(unreadCount)}
              </Text>
            ) : (
              <View style={styles.badgeDot} />
            )}
          </View>
        )}
      </Animated.View>
    </TouchableOpacity>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      position: 'relative',
    },
    iconContainer: {
      position: 'relative',
    },
    badge: {
      position: 'absolute',
      top: -4,
      right: -4,
      backgroundColor: '#FF6B6B',
      justifyContent: 'center',
      alignItems: 'center',
      minWidth: 20,
      paddingHorizontal: 4,
    },
    badgeText: {
      color: '#FFFFFF',
      fontWeight: FONT_WEIGHTS.bold,
      textAlign: 'center',
    },
    badgeDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: '#FFFFFF',
    },
  });
