import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  View,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useThemedStyles, useAuth } from '../hooks';
import ChatService from '../services/ChatService';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../constants/theme';
import type { ThemeColors } from '../contexts/ThemeContext';

interface ChatButtonProps {
  targetUserId: string;
  targetUserName: string;
  targetUserAvatar?: string;
  style?: any;
  variant?: 'primary' | 'secondary' | 'icon';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  onPress?: () => void;
}

export const ChatButton: React.FC<ChatButtonProps> = ({
  targetUserId,
  targetUserName,
  targetUserAvatar,
  style,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  onPress,
}) => {
  const styles = useThemedStyles(createStyles);
  const navigation = useNavigation();
  const { user } = useAuth();

  const handlePress = async () => {
    if (disabled || !user) return;

    try {
      // Custom onPress handler takes precedence
      if (onPress) {
        onPress();
        return;
      }

      // Connect to chat service if not already connected
      if (!ChatService.isConnected) {
        await ChatService.connect(user.id);
      }

      // Navigate to chat screen
      navigation.navigate('Chat' as never, {
        otherUserId: targetUserId,
        otherUserName: targetUserName,
        otherUserAvatar: targetUserAvatar,
      } as never);
    } catch (error) {
      console.error('Error opening chat:', error);
    }
  };

  const getButtonStyle = () => {
    const baseStyle = [styles.button];
    
    // Add variant styles
    switch (variant) {
      case 'primary':
        baseStyle.push(styles.primaryButton);
        break;
      case 'secondary':
        baseStyle.push(styles.secondaryButton);
        break;
      case 'icon':
        baseStyle.push(styles.iconButton);
        break;
    }

    // Add size styles
    switch (size) {
      case 'small':
        baseStyle.push(styles.smallButton);
        break;
      case 'medium':
        baseStyle.push(styles.mediumButton);
        break;
      case 'large':
        baseStyle.push(styles.largeButton);
        break;
    }

    // Add disabled style
    if (disabled) {
      baseStyle.push(styles.disabledButton);
    }

    return baseStyle;
  };

  const getTextStyle = () => {
    const baseStyle = [styles.buttonText];
    
    switch (variant) {
      case 'primary':
        baseStyle.push(styles.primaryButtonText);
        break;
      case 'secondary':
        baseStyle.push(styles.secondaryButtonText);
        break;
    }

    switch (size) {
      case 'small':
        baseStyle.push(styles.smallButtonText);
        break;
      case 'medium':
        baseStyle.push(styles.mediumButtonText);
        break;
      case 'large':
        baseStyle.push(styles.largeButtonText);
        break;
    }

    if (disabled) {
      baseStyle.push(styles.disabledButtonText);
    }

    return baseStyle;
  };

  const getIconSize = () => {
    switch (size) {
      case 'small': return 16;
      case 'medium': return 20;
      case 'large': return 24;
      default: return 20;
    }
  };

  const getIconColor = () => {
    if (disabled) return '#999';
    
    switch (variant) {
      case 'primary': return '#FFFFFF';
      case 'secondary': return '#667eea';
      case 'icon': return '#667eea';
      default: return '#FFFFFF';
    }
  };

  const renderContent = () => {
    if (variant === 'icon') {
      return (
        <Ionicons 
          name="chatbubble-outline" 
          size={getIconSize()} 
          color={getIconColor()} 
        />
      );
    }

    return (
      <View style={styles.buttonContent}>
        <Ionicons 
          name="chatbubble-outline" 
          size={getIconSize()} 
          color={getIconColor()} 
        />
        <Text style={getTextStyle()}>
          {size === 'small' ? 'Chat' : 'Message'}
        </Text>
      </View>
    );
  };

  // Don't show chat button if user is trying to chat with themselves
  if (user?.id === targetUserId) {
    return null;
  }

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={handlePress}
      disabled={disabled}
      activeOpacity={0.7}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  button: {
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  primaryButton: {
    backgroundColor: '#667eea',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#667eea',
  },
  iconButton: {
    backgroundColor: 'transparent',
    borderRadius: BORDER_RADIUS.full,
    width: 40,
    height: 40,
  },
  smallButton: {
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    minHeight: 32,
  },
  mediumButton: {
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    minHeight: 40,
  },
  largeButton: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    minHeight: 48,
  },
  disabledButton: {
    backgroundColor: colors.surface,
    borderColor: colors.border,
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  buttonText: {
    fontWeight: FONT_WEIGHTS.medium,
  },
  primaryButtonText: {
    color: '#FFFFFF',
  },
  secondaryButtonText: {
    color: '#667eea',
  },
  smallButtonText: {
    fontSize: FONT_SIZES.sm,
  },
  mediumButtonText: {
    fontSize: FONT_SIZES.md,
  },
  largeButtonText: {
    fontSize: FONT_SIZES.lg,
  },
  disabledButtonText: {
    color: '#999',
  },
});
