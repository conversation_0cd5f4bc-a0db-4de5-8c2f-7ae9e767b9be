import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { VENDOR_STATUS, ORDER_STATUS, PRODUCT_CATEGORIES } from '../constants';
import { sampleVendors, sampleProducts, sampleOrders } from '../data/sampleData';
import type { VendorStatus, OrderStatus, ProductCategory } from '../constants';

// Types
export interface Vendor {
  id: string;
  businessName: string;
  businessDescription: string;
  ownerName: string;
  email: string;
  phone: string;
  businessLogo?: string;
  status: VendorStatus;
  rating: number;
  totalProducts: number;
  totalOrders: number;
  revenue: number;
  createdAt: string;
  approvedAt?: string;
  rejectedAt?: string;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
}

export interface Product {
  id: string;
  vendorId: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  category: ProductCategory;
  images: string[];
  inventory: number;
  isActive: boolean;
  rating: number;
  reviewCount: number;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  specifications?: Record<string, string>;
}

export interface Order {
  id: string;
  customerId: string;
  customerName: string;
  customerEmail: string;
  items: OrderItem[];
  totalAmount: number;
  status: OrderStatus;
  shippingAddress: Address;
  paymentMethod: string;
  createdAt: string;
  updatedAt: string;
  deliveredAt?: string;
  trackingNumber?: string;
}

export interface OrderItem {
  id: string;
  productId: string;
  vendorId: string;
  productName: string;
  productImage: string;
  quantity: number;
  price: number;
  totalPrice: number;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface CartItem {
  id: string;
  productId: string;
  vendorId: string;
  quantity: number;
  product: Product;
}

export interface DataState {
  vendors: Vendor[];
  products: Product[];
  orders: Order[];
  cart: CartItem[];
  isLoading: boolean;
  error: string | null;
}

type DataAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_VENDORS'; payload: Vendor[] }
  | { type: 'SET_PRODUCTS'; payload: Product[] }
  | { type: 'SET_ORDERS'; payload: Order[] }
  | { type: 'UPDATE_VENDOR'; payload: Vendor }
  | { type: 'ADD_PRODUCT'; payload: Product }
  | { type: 'UPDATE_PRODUCT'; payload: Product }
  | { type: 'DELETE_PRODUCT'; payload: string }
  | { type: 'ADD_ORDER'; payload: Order }
  | { type: 'UPDATE_ORDER'; payload: Order }
  | { type: 'ADD_TO_CART'; payload: CartItem }
  | { type: 'UPDATE_CART_ITEM'; payload: { productId: string; quantity: number } }
  | { type: 'REMOVE_FROM_CART'; payload: string }
  | { type: 'CLEAR_CART' };

interface DataContextType extends DataState {
  // Vendor methods
  approveVendor: (vendorId: string) => Promise<void>;
  rejectVendor: (vendorId: string) => Promise<void>;
  getVendorById: (vendorId: string) => Vendor | undefined;
  getVendorProducts: (vendorId: string) => Product[];
  getVendorOrders: (vendorId: string) => Order[];
  
  // Product methods
  addProduct: (product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateProduct: (productId: string, updates: Partial<Product>) => Promise<void>;
  deleteProduct: (productId: string) => Promise<void>;
  getProductById: (productId: string) => Product | undefined;
  searchProducts: (query: string) => Product[];
  getProductsByCategory: (category: ProductCategory) => Product[];
  
  // Order methods
  createOrder: (orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateOrderStatus: (orderId: string, status: OrderStatus) => Promise<void>;
  getOrderById: (orderId: string) => Order | undefined;
  getCustomerOrders: (customerId: string) => Order[];
  
  // Cart methods
  addToCart: (product: Product, quantity: number) => void;
  updateCartItemQuantity: (productId: string, quantity: number) => void;
  removeFromCart: (productId: string) => void;
  clearCart: () => void;
  getCartTotal: () => number;
  getCartItemCount: () => number;
  
  // Analytics
  getPlatformStats: () => {
    totalVendors: number;
    approvedVendors: number;
    pendingVendors: number;
    totalProducts: number;
    totalOrders: number;
    totalRevenue: number;
  };
}

// Initial state
const initialState: DataState = {
  vendors: [],
  products: [],
  orders: [],
  cart: [],
  isLoading: false,
  error: null,
};

// Reducer
const dataReducer = (state: DataState, action: DataAction): DataState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'SET_VENDORS':
      return { ...state, vendors: action.payload };
    
    case 'SET_PRODUCTS':
      return { ...state, products: action.payload };
    
    case 'SET_ORDERS':
      return { ...state, orders: action.payload };
    
    case 'UPDATE_VENDOR':
      return {
        ...state,
        vendors: state.vendors.map(vendor =>
          vendor.id === action.payload.id ? action.payload : vendor
        ),
      };
    
    case 'ADD_PRODUCT':
      return { ...state, products: [...state.products, action.payload] };
    
    case 'UPDATE_PRODUCT':
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id ? action.payload : product
        ),
      };
    
    case 'DELETE_PRODUCT':
      return {
        ...state,
        products: state.products.filter(product => product.id !== action.payload),
      };
    
    case 'ADD_ORDER':
      return { ...state, orders: [...state.orders, action.payload] };
    
    case 'UPDATE_ORDER':
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? action.payload : order
        ),
      };
    
    case 'ADD_TO_CART':
      const existingItemIndex = state.cart.findIndex(
        item => item.productId === action.payload.productId
      );
      
      if (existingItemIndex >= 0) {
        const updatedCart = [...state.cart];
        updatedCart[existingItemIndex].quantity += action.payload.quantity;
        return { ...state, cart: updatedCart };
      }
      
      return { ...state, cart: [...state.cart, action.payload] };
    
    case 'UPDATE_CART_ITEM':
      return {
        ...state,
        cart: state.cart.map(item =>
          item.productId === action.payload.productId
            ? { ...item, quantity: action.payload.quantity }
            : item
        ),
      };
    
    case 'REMOVE_FROM_CART':
      return {
        ...state,
        cart: state.cart.filter(item => item.productId !== action.payload),
      };
    
    case 'CLEAR_CART':
      return { ...state, cart: [] };
    
    default:
      return state;
  }
};

// Create context
const DataContext = createContext<DataContextType | undefined>(undefined);

// Provider component
export const DataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(dataReducer, initialState);

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      // Load sample data (in a real app, this would be API calls)
      // Using static imports instead of dynamic imports to avoid Metro bundler issues
      dispatch({ type: 'SET_VENDORS', payload: sampleVendors });
      dispatch({ type: 'SET_PRODUCTS', payload: sampleProducts });
      dispatch({ type: 'SET_ORDERS', payload: sampleOrders });
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load data' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Vendor methods
  const approveVendor = async (vendorId: string) => {
    const vendor = state.vendors.find(v => v.id === vendorId);
    if (vendor) {
      const updatedVendor = {
        ...vendor,
        status: VENDOR_STATUS.APPROVED as VendorStatus,
        approvedAt: new Date().toISOString(),
      };
      dispatch({ type: 'UPDATE_VENDOR', payload: updatedVendor });
    }
  };

  const rejectVendor = async (vendorId: string) => {
    const vendor = state.vendors.find(v => v.id === vendorId);
    if (vendor) {
      const updatedVendor = {
        ...vendor,
        status: VENDOR_STATUS.REJECTED as VendorStatus,
        rejectedAt: new Date().toISOString(),
      };
      dispatch({ type: 'UPDATE_VENDOR', payload: updatedVendor });
    }
  };

  const getVendorById = (vendorId: string) => {
    return state.vendors.find(vendor => vendor.id === vendorId);
  };

  const getVendorProducts = (vendorId: string) => {
    return state.products.filter(product => product.vendorId === vendorId);
  };

  const getVendorOrders = (vendorId: string) => {
    return state.orders.filter(order =>
      order.items.some(item => item.vendorId === vendorId)
    );
  };

  // Product methods
  const addProduct = async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newProduct: Product = {
      ...productData,
      id: 'product-' + Date.now(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    dispatch({ type: 'ADD_PRODUCT', payload: newProduct });
  };

  const updateProduct = async (productId: string, updates: Partial<Product>) => {
    const product = state.products.find(p => p.id === productId);
    if (product) {
      const updatedProduct = {
        ...product,
        ...updates,
        updatedAt: new Date().toISOString(),
      };
      dispatch({ type: 'UPDATE_PRODUCT', payload: updatedProduct });
    }
  };

  const deleteProduct = async (productId: string) => {
    dispatch({ type: 'DELETE_PRODUCT', payload: productId });
  };

  const getProductById = (productId: string) => {
    return state.products.find(product => product.id === productId);
  };

  const searchProducts = (query: string) => {
    const lowercaseQuery = query.toLowerCase();
    return state.products.filter(product =>
      product.name.toLowerCase().includes(lowercaseQuery) ||
      product.description.toLowerCase().includes(lowercaseQuery) ||
      product.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  };

  const getProductsByCategory = (category: ProductCategory) => {
    return state.products.filter(product => product.category === category);
  };

  // Order methods
  const createOrder = async (orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newOrder: Order = {
      ...orderData,
      id: 'order-' + Date.now(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    dispatch({ type: 'ADD_ORDER', payload: newOrder });
  };

  const updateOrderStatus = async (orderId: string, status: OrderStatus) => {
    const order = state.orders.find(o => o.id === orderId);
    if (order) {
      const updatedOrder = {
        ...order,
        status,
        updatedAt: new Date().toISOString(),
        deliveredAt: status === ORDER_STATUS.DELIVERED ? new Date().toISOString() : order.deliveredAt,
      };
      dispatch({ type: 'UPDATE_ORDER', payload: updatedOrder });
    }
  };

  const getOrderById = (orderId: string) => {
    return state.orders.find(order => order.id === orderId);
  };

  const getCustomerOrders = (customerId: string) => {
    return state.orders.filter(order => order.customerId === customerId);
  };

  // Cart methods
  const addToCart = (product: Product, quantity: number) => {
    const cartItem: CartItem = {
      id: 'cart-item-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
      productId: product.id,
      vendorId: product.vendorId,
      quantity,
      product,
    };
    dispatch({ type: 'ADD_TO_CART', payload: cartItem });
  };

  const updateCartItemQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(productId);
    } else {
      dispatch({ type: 'UPDATE_CART_ITEM', payload: { productId, quantity } });
    }
  };

  const removeFromCart = (productId: string) => {
    dispatch({ type: 'REMOVE_FROM_CART', payload: productId });
  };

  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
  };

  const getCartTotal = () => {
    return state.cart.reduce((total, item) => total + (item.product.price * item.quantity), 0);
  };

  const getCartItemCount = () => {
    return state.cart.reduce((count, item) => count + item.quantity, 0);
  };

  // Analytics
  const getPlatformStats = () => {
    const totalVendors = state.vendors.length;
    const approvedVendors = state.vendors.filter(v => v.status === VENDOR_STATUS.APPROVED).length;
    const pendingVendors = state.vendors.filter(v => v.status === VENDOR_STATUS.PENDING).length;
    const totalProducts = state.products.length;
    const totalOrders = state.orders.length;
    const totalRevenue = state.orders.reduce((sum, order) => sum + order.totalAmount, 0);

    return {
      totalVendors,
      approvedVendors,
      pendingVendors,
      totalProducts,
      totalOrders,
      totalRevenue,
    };
  };

  const value: DataContextType = {
    ...state,
    approveVendor,
    rejectVendor,
    getVendorById,
    getVendorProducts,
    getVendorOrders,
    addProduct,
    updateProduct,
    deleteProduct,
    getProductById,
    searchProducts,
    getProductsByCategory,
    createOrder,
    updateOrderStatus,
    getOrderById,
    getCustomerOrders,
    addToCart,
    updateCartItemQuantity,
    removeFromCart,
    clearCart,
    getCartTotal,
    getCartItemCount,
    getPlatformStats,
  };

  return <DataContext.Provider value={value}>{children}</DataContext.Provider>;
};

// Hook to use data context
export const useData = (): DataContextType => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};
