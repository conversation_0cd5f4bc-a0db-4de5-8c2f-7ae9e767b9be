import { sampleVendors, sampleProducts, sampleOrders } from './sampleData';
import { delay } from '../utils';
import type { Vendor, Product, Order } from '../contexts/DataContext';
import type { VendorStatus, OrderStatus } from '../constants';

// Simulate network delay
const NETWORK_DELAY = 500;

// Mock API responses
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

interface PaginatedResponse<T> {
  success: boolean;
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Vendor API
export const vendorApi = {
  // Get all vendors
  getVendors: async (
    page: number = 1,
    limit: number = 10,
    status?: VendorStatus
  ): Promise<PaginatedResponse<Vendor>> => {
    await delay(NETWORK_DELAY);
    
    let filteredVendors = [...sampleVendors];
    
    if (status) {
      filteredVendors = filteredVendors.filter(vendor => vendor.status === status);
    }
    
    const total = filteredVendors.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedVendors = filteredVendors.slice(startIndex, endIndex);
    
    return {
      success: true,
      data: paginatedVendors,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    };
  },

  // Get vendor by ID
  getVendorById: async (vendorId: string): Promise<ApiResponse<Vendor>> => {
    await delay(NETWORK_DELAY);
    
    const vendor = sampleVendors.find(v => v.id === vendorId);
    
    if (!vendor) {
      return {
        success: false,
        error: 'Vendor not found',
      };
    }
    
    return {
      success: true,
      data: vendor,
    };
  },

  // Approve vendor
  approveVendor: async (vendorId: string): Promise<ApiResponse<Vendor>> => {
    await delay(NETWORK_DELAY);
    
    const vendorIndex = sampleVendors.findIndex(v => v.id === vendorId);
    
    if (vendorIndex === -1) {
      return {
        success: false,
        error: 'Vendor not found',
      };
    }
    
    // Simulate approval
    const updatedVendor = {
      ...sampleVendors[vendorIndex],
      status: 'approved' as VendorStatus,
      approvedAt: new Date().toISOString(),
    };
    
    return {
      success: true,
      data: updatedVendor,
      message: 'Vendor approved successfully',
    };
  },

  // Reject vendor
  rejectVendor: async (vendorId: string): Promise<ApiResponse<Vendor>> => {
    await delay(NETWORK_DELAY);
    
    const vendorIndex = sampleVendors.findIndex(v => v.id === vendorId);
    
    if (vendorIndex === -1) {
      return {
        success: false,
        error: 'Vendor not found',
      };
    }
    
    // Simulate rejection
    const updatedVendor = {
      ...sampleVendors[vendorIndex],
      status: 'rejected' as VendorStatus,
      rejectedAt: new Date().toISOString(),
    };
    
    return {
      success: true,
      data: updatedVendor,
      message: 'Vendor rejected successfully',
    };
  },

  // Search vendors
  searchVendors: async (query: string): Promise<ApiResponse<Vendor[]>> => {
    await delay(NETWORK_DELAY);
    
    const lowercaseQuery = query.toLowerCase();
    const filteredVendors = sampleVendors.filter(vendor =>
      vendor.businessName.toLowerCase().includes(lowercaseQuery) ||
      vendor.businessDescription.toLowerCase().includes(lowercaseQuery) ||
      vendor.ownerName.toLowerCase().includes(lowercaseQuery)
    );
    
    return {
      success: true,
      data: filteredVendors,
    };
  },
};

// Product API
export const productApi = {
  // Get all products
  getProducts: async (
    page: number = 1,
    limit: number = 10,
    vendorId?: string
  ): Promise<PaginatedResponse<Product>> => {
    await delay(NETWORK_DELAY);
    
    let filteredProducts = [...sampleProducts];
    
    if (vendorId) {
      filteredProducts = filteredProducts.filter(product => product.vendorId === vendorId);
    }
    
    const total = filteredProducts.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);
    
    return {
      success: true,
      data: paginatedProducts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    };
  },

  // Get product by ID
  getProductById: async (productId: string): Promise<ApiResponse<Product>> => {
    await delay(NETWORK_DELAY);
    
    const product = sampleProducts.find(p => p.id === productId);
    
    if (!product) {
      return {
        success: false,
        error: 'Product not found',
      };
    }
    
    return {
      success: true,
      data: product,
    };
  },

  // Create product
  createProduct: async (productData: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Product>> => {
    await delay(NETWORK_DELAY);
    
    const newProduct: Product = {
      ...productData,
      id: 'product-' + Date.now(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    return {
      success: true,
      data: newProduct,
      message: 'Product created successfully',
    };
  },

  // Update product
  updateProduct: async (productId: string, updates: Partial<Product>): Promise<ApiResponse<Product>> => {
    await delay(NETWORK_DELAY);
    
    const productIndex = sampleProducts.findIndex(p => p.id === productId);
    
    if (productIndex === -1) {
      return {
        success: false,
        error: 'Product not found',
      };
    }
    
    const updatedProduct = {
      ...sampleProducts[productIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    };
    
    return {
      success: true,
      data: updatedProduct,
      message: 'Product updated successfully',
    };
  },

  // Delete product
  deleteProduct: async (productId: string): Promise<ApiResponse<null>> => {
    await delay(NETWORK_DELAY);
    
    const productIndex = sampleProducts.findIndex(p => p.id === productId);
    
    if (productIndex === -1) {
      return {
        success: false,
        error: 'Product not found',
      };
    }
    
    return {
      success: true,
      data: null,
      message: 'Product deleted successfully',
    };
  },

  // Search products
  searchProducts: async (query: string): Promise<ApiResponse<Product[]>> => {
    await delay(NETWORK_DELAY);
    
    const lowercaseQuery = query.toLowerCase();
    const filteredProducts = sampleProducts.filter(product =>
      product.name.toLowerCase().includes(lowercaseQuery) ||
      product.description.toLowerCase().includes(lowercaseQuery) ||
      product.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
    
    return {
      success: true,
      data: filteredProducts,
    };
  },
};

// Order API
export const orderApi = {
  // Get all orders
  getOrders: async (
    page: number = 1,
    limit: number = 10,
    customerId?: string,
    vendorId?: string
  ): Promise<PaginatedResponse<Order>> => {
    await delay(NETWORK_DELAY);
    
    let filteredOrders = [...sampleOrders];
    
    if (customerId) {
      filteredOrders = filteredOrders.filter(order => order.customerId === customerId);
    }
    
    if (vendorId) {
      filteredOrders = filteredOrders.filter(order =>
        order.items.some(item => item.vendorId === vendorId)
      );
    }
    
    const total = filteredOrders.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedOrders = filteredOrders.slice(startIndex, endIndex);
    
    return {
      success: true,
      data: paginatedOrders,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    };
  },

  // Get order by ID
  getOrderById: async (orderId: string): Promise<ApiResponse<Order>> => {
    await delay(NETWORK_DELAY);
    
    const order = sampleOrders.find(o => o.id === orderId);
    
    if (!order) {
      return {
        success: false,
        error: 'Order not found',
      };
    }
    
    return {
      success: true,
      data: order,
    };
  },

  // Create order
  createOrder: async (orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Order>> => {
    await delay(NETWORK_DELAY);
    
    const newOrder: Order = {
      ...orderData,
      id: 'order-' + Date.now(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    return {
      success: true,
      data: newOrder,
      message: 'Order created successfully',
    };
  },

  // Update order status
  updateOrderStatus: async (orderId: string, status: OrderStatus): Promise<ApiResponse<Order>> => {
    await delay(NETWORK_DELAY);
    
    const orderIndex = sampleOrders.findIndex(o => o.id === orderId);
    
    if (orderIndex === -1) {
      return {
        success: false,
        error: 'Order not found',
      };
    }
    
    const updatedOrder = {
      ...sampleOrders[orderIndex],
      status,
      updatedAt: new Date().toISOString(),
      deliveredAt: status === 'delivered' ? new Date().toISOString() : sampleOrders[orderIndex].deliveredAt,
    };
    
    return {
      success: true,
      data: updatedOrder,
      message: 'Order status updated successfully',
    };
  },
};

// Analytics API
export const analyticsApi = {
  // Get platform statistics
  getPlatformStats: async (): Promise<ApiResponse<any>> => {
    await delay(NETWORK_DELAY);
    
    const totalVendors = sampleVendors.length;
    const approvedVendors = sampleVendors.filter(v => v.status === 'approved').length;
    const pendingVendors = sampleVendors.filter(v => v.status === 'pending').length;
    const totalProducts = sampleProducts.length;
    const activeProducts = sampleProducts.filter(p => p.isActive).length;
    const totalOrders = sampleOrders.length;
    const totalRevenue = sampleOrders.reduce((sum, order) => sum + order.totalAmount, 0);
    
    return {
      success: true,
      data: {
        totalVendors,
        approvedVendors,
        pendingVendors,
        totalProducts,
        activeProducts,
        totalOrders,
        totalRevenue,
        averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0,
      },
    };
  },

  // Get vendor statistics
  getVendorStats: async (vendorId: string): Promise<ApiResponse<any>> => {
    await delay(NETWORK_DELAY);
    
    const vendor = sampleVendors.find(v => v.id === vendorId);
    
    if (!vendor) {
      return {
        success: false,
        error: 'Vendor not found',
      };
    }
    
    const vendorProducts = sampleProducts.filter(p => p.vendorId === vendorId);
    const vendorOrders = sampleOrders.filter(order =>
      order.items.some(item => item.vendorId === vendorId)
    );
    
    const totalRevenue = vendorOrders.reduce((sum, order) => sum + order.totalAmount, 0);
    
    return {
      success: true,
      data: {
        vendor,
        totalProducts: vendorProducts.length,
        activeProducts: vendorProducts.filter(p => p.isActive).length,
        totalOrders: vendorOrders.length,
        totalRevenue,
        averageOrderValue: vendorOrders.length > 0 ? totalRevenue / vendorOrders.length : 0,
      },
    };
  },
};
