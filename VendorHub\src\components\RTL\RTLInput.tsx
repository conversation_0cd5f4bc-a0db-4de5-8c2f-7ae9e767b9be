import React, { useMemo } from 'react';
import { TextInput, TextInputProps, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { useI18n } from '../../hooks/useI18n';
import { useFont } from '../../hooks/useFont';

interface RTLInputProps extends TextInputProps {
  style?: ViewStyle | ViewStyle[];
  weight?: 'regular' | 'medium' | 'bold' | 'light';
}

export const RTLInput: React.FC<RTLInputProps> = ({ 
  style, 
  weight = 'regular',
  textAlign,
  ...props 
}) => {
  const { isRTL, currentLanguage } = useI18n();
  
  // Get appropriate font style for current language
  const fontStyle = useFont({ weight });
  
  const rtlStyle = useMemo(() => {
    const baseStyle: TextStyle = {
      ...fontStyle,
      textAlign: textAlign || (isRTL ? 'right' : 'left'),
    };
    
    if (!style) {
      return baseStyle;
    }
    
    const flattenedStyle = StyleSheet.flatten(style);
    const rtlFlattenedStyle = { ...baseStyle, ...flattenedStyle };
    
    // Handle RTL-specific styling
    if (isRTL) {
      // Flip padding if specified
      if (flattenedStyle.paddingLeft !== undefined && flattenedStyle.paddingRight === undefined) {
        rtlFlattenedStyle.paddingRight = flattenedStyle.paddingLeft;
        rtlFlattenedStyle.paddingLeft = 0;
      } else if (flattenedStyle.paddingRight !== undefined && flattenedStyle.paddingLeft === undefined) {
        rtlFlattenedStyle.paddingLeft = flattenedStyle.paddingRight;
        rtlFlattenedStyle.paddingRight = 0;
      }
      
      // Flip text alignment if not explicitly set
      if (!textAlign && flattenedStyle.textAlign === 'left') {
        rtlFlattenedStyle.textAlign = 'right';
      } else if (!textAlign && flattenedStyle.textAlign === 'right') {
        rtlFlattenedStyle.textAlign = 'left';
      }
    }
    
    return rtlFlattenedStyle;
  }, [style, isRTL, fontStyle, textAlign]);
  
  return (
    <TextInput
      style={rtlStyle}
      textAlign={textAlign || (isRTL ? 'right' : 'left')}
      {...props}
    />
  );
};
