import { EventEmitter } from '../utils/EventEmitter';
import type { Product } from '../contexts/DataContext';
import { storage } from '../utils/storage';

export interface SearchQuery {
  id: string;
  query: string;
  timestamp: string;
  userId?: string;
  resultsCount: number;
  clickedResults: string[];
}

export interface SearchSuggestion {
  text: string;
  type: 'query' | 'product' | 'category' | 'brand';
  score: number;
  metadata?: Record<string, any>;
}

export interface SearchFilter {
  category?: string[];
  priceRange?: { min: number; max: number };
  rating?: number;
  inStock?: boolean;
  onSale?: boolean;
  brands?: string[];
  vendors?: string[];
  location?: string;
  sortBy?: 'relevance' | 'price_low' | 'price_high' | 'rating' | 'newest' | 'popularity';
}

export interface SearchResult {
  product: Product;
  score: number;
  matchedFields: string[];
  highlights: Record<string, string>;
}

export interface VoiceSearchResult {
  transcript: string;
  confidence: number;
  alternatives?: Array<{ transcript: string; confidence: number }>;
}

export interface BarcodeSearchResult {
  barcode: string;
  product?: Product;
  found: boolean;
}

class AdvancedSearchService extends EventEmitter {
  private static instance: AdvancedSearchService;
  private searchHistory: SearchQuery[] = [];
  private popularQueries: Map<string, number> = new Map();
  private searchSuggestions: SearchSuggestion[] = [];
  private isVoiceSearchEnabled: boolean = false;

  private constructor() {
    super();
    this.loadStoredData();
    this.initializeSearchSuggestions();
  }

  public static getInstance(): AdvancedSearchService {
    if (!AdvancedSearchService.instance) {
      AdvancedSearchService.instance = new AdvancedSearchService();
    }
    return AdvancedSearchService.instance;
  }

  // Text Search
  public async search(
    query: string,
    products: Product[],
    filters: SearchFilter = {},
    userId?: string
  ): Promise<SearchResult[]> {
    const normalizedQuery = this.normalizeQuery(query);
    
    // Record search
    await this.recordSearch(query, userId);
    
    // Apply filters first
    let filteredProducts = this.applyFilters(products, filters);
    
    // Perform search
    const results = this.performTextSearch(normalizedQuery, filteredProducts);
    
    // Sort results
    const sortedResults = this.sortResults(results, filters.sortBy || 'relevance');
    
    // Update search analytics
    this.updateSearchAnalytics(query, sortedResults.length);
    
    return sortedResults;
  }

  // AI-Powered Search Suggestions
  public async getSearchSuggestions(
    partialQuery: string,
    products: Product[],
    limit: number = 10
  ): Promise<SearchSuggestion[]> {
    const normalizedQuery = partialQuery.toLowerCase().trim();
    
    if (normalizedQuery.length < 2) {
      return this.getPopularSuggestions(limit);
    }

    const suggestions: SearchSuggestion[] = [];

    // Query completions from history
    const queryCompletions = this.getQueryCompletions(normalizedQuery);
    suggestions.push(...queryCompletions);

    // Product name suggestions
    const productSuggestions = this.getProductSuggestions(normalizedQuery, products);
    suggestions.push(...productSuggestions);

    // Category suggestions
    const categorySuggestions = this.getCategorySuggestions(normalizedQuery, products);
    suggestions.push(...categorySuggestions);

    // Brand suggestions
    const brandSuggestions = this.getBrandSuggestions(normalizedQuery, products);
    suggestions.push(...brandSuggestions);

    // Sort by score and return top suggestions
    return suggestions
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  // Voice Search
  public async enableVoiceSearch(): Promise<boolean> {
    try {
      // Check if speech recognition is available
      if (typeof window !== 'undefined' && 'webkitSpeechRecognition' in window) {
        this.isVoiceSearchEnabled = true;
        return true;
      }
      return false;
    } catch (error) {
      console.error('Voice search not available:', error);
      return false;
    }
  }

  public async performVoiceSearch(): Promise<VoiceSearchResult> {
    if (!this.isVoiceSearchEnabled) {
      throw new Error('Voice search not enabled');
    }

    return new Promise((resolve, reject) => {
      try {
        const recognition = new (window as any).webkitSpeechRecognition();
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'en-US';

        recognition.onresult = (event: any) => {
          const result = event.results[0];
          const transcript = result[0].transcript;
          const confidence = result[0].confidence;

          const alternatives = Array.from(result).map((alt: any) => ({
            transcript: alt.transcript,
            confidence: alt.confidence,
          }));

          resolve({
            transcript,
            confidence,
            alternatives,
          });
        };

        recognition.onerror = (event: any) => {
          reject(new Error(`Voice recognition error: ${event.error}`));
        };

        recognition.start();
      } catch (error) {
        reject(error);
      }
    });
  }

  // Barcode Search
  public async searchByBarcode(barcode: string, products: Product[]): Promise<BarcodeSearchResult> {
    // In a real implementation, this would query a barcode database
    // For now, we'll simulate barcode matching
    const product = products.find(p => 
      p.metadata?.barcode === barcode || 
      p.id === barcode
    );

    return {
      barcode,
      product,
      found: !!product,
    };
  }

  // Visual Search (Image-based)
  public async searchByImage(imageUri: string, products: Product[]): Promise<SearchResult[]> {
    // Simulate image-based search
    // In a real implementation, this would use computer vision APIs
    
    // For demonstration, return random products
    const randomProducts = products
      .sort(() => Math.random() - 0.5)
      .slice(0, 5)
      .map(product => ({
        product,
        score: Math.random() * 0.8 + 0.2, // Random score between 0.2 and 1.0
        matchedFields: ['image'],
        highlights: {},
      }));

    return randomProducts;
  }

  // Faceted Search
  public getFacets(products: Product[]): Record<string, Array<{ value: string; count: number }>> {
    const facets: Record<string, Map<string, number>> = {
      categories: new Map(),
      brands: new Map(),
      priceRanges: new Map(),
      ratings: new Map(),
    };

    products.forEach(product => {
      // Categories
      facets.categories.set(
        product.category,
        (facets.categories.get(product.category) || 0) + 1
      );

      // Brands (if available in metadata)
      const brand = product.metadata?.brand || 'Unknown';
      facets.brands.set(brand, (facets.brands.get(brand) || 0) + 1);

      // Price ranges
      const priceRange = this.getPriceRange(product.price);
      facets.priceRanges.set(priceRange, (facets.priceRanges.get(priceRange) || 0) + 1);

      // Ratings
      const ratingRange = Math.floor(product.rating);
      const ratingKey = `${ratingRange}+ stars`;
      facets.ratings.set(ratingKey, (facets.ratings.get(ratingKey) || 0) + 1);
    });

    // Convert to array format
    const result: Record<string, Array<{ value: string; count: number }>> = {};
    Object.entries(facets).forEach(([key, valueMap]) => {
      result[key] = Array.from(valueMap.entries())
        .map(([value, count]) => ({ value, count }))
        .sort((a, b) => b.count - a.count);
    });

    return result;
  }

  // Search Analytics
  public getSearchAnalytics(): {
    topQueries: Array<{ query: string; count: number }>;
    noResultsQueries: Array<{ query: string; count: number }>;
    averageResultsPerQuery: number;
    searchTrends: Array<{ date: string; searches: number }>;
  } {
    const queryStats = new Map<string, { count: number; totalResults: number }>();
    const noResultsQueries = new Map<string, number>();
    const dailySearches = new Map<string, number>();

    this.searchHistory.forEach(search => {
      const query = search.query.toLowerCase();
      const date = search.timestamp.split('T')[0];

      // Query statistics
      const current = queryStats.get(query) || { count: 0, totalResults: 0 };
      queryStats.set(query, {
        count: current.count + 1,
        totalResults: current.totalResults + search.resultsCount,
      });

      // No results tracking
      if (search.resultsCount === 0) {
        noResultsQueries.set(query, (noResultsQueries.get(query) || 0) + 1);
      }

      // Daily search trends
      dailySearches.set(date, (dailySearches.get(date) || 0) + 1);
    });

    const topQueries = Array.from(queryStats.entries())
      .map(([query, stats]) => ({ query, count: stats.count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const noResultsQueriesArray = Array.from(noResultsQueries.entries())
      .map(([query, count]) => ({ query, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    const totalResults = Array.from(queryStats.values())
      .reduce((sum, stats) => sum + stats.totalResults, 0);
    const averageResultsPerQuery = this.searchHistory.length > 0 
      ? totalResults / this.searchHistory.length 
      : 0;

    const searchTrends = Array.from(dailySearches.entries())
      .map(([date, searches]) => ({ date, searches }))
      .sort((a, b) => a.date.localeCompare(b.date))
      .slice(-30); // Last 30 days

    return {
      topQueries,
      noResultsQueries: noResultsQueriesArray,
      averageResultsPerQuery,
      searchTrends,
    };
  }

  // Private Methods
  private normalizeQuery(query: string): string {
    return query.toLowerCase().trim().replace(/\s+/g, ' ');
  }

  private async recordSearch(query: string, userId?: string): Promise<void> {
    const searchQuery: SearchQuery = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      query,
      timestamp: new Date().toISOString(),
      userId,
      resultsCount: 0, // Will be updated later
      clickedResults: [],
    };

    this.searchHistory.push(searchQuery);

    // Keep only last 1000 searches
    if (this.searchHistory.length > 1000) {
      this.searchHistory.splice(0, this.searchHistory.length - 1000);
    }

    // Update popular queries
    const normalizedQuery = this.normalizeQuery(query);
    this.popularQueries.set(normalizedQuery, (this.popularQueries.get(normalizedQuery) || 0) + 1);

    await this.saveData();
  }

  private applyFilters(products: Product[], filters: SearchFilter): Product[] {
    return products.filter(product => {
      // Category filter
      if (filters.category && filters.category.length > 0) {
        if (!filters.category.includes(product.category)) return false;
      }

      // Price range filter
      if (filters.priceRange) {
        if (product.price < filters.priceRange.min || product.price > filters.priceRange.max) {
          return false;
        }
      }

      // Rating filter
      if (filters.rating && product.rating < filters.rating) {
        return false;
      }

      // In stock filter
      if (filters.inStock && product.inventory <= 0) {
        return false;
      }

      // On sale filter
      if (filters.onSale && (!product.originalPrice || product.originalPrice <= product.price)) {
        return false;
      }

      // Vendor filter
      if (filters.vendors && filters.vendors.length > 0) {
        if (!filters.vendors.includes(product.vendorId)) return false;
      }

      return true;
    });
  }

  private performTextSearch(query: string, products: Product[]): SearchResult[] {
    const queryTerms = query.split(' ').filter(term => term.length > 0);
    
    return products.map(product => {
      const score = this.calculateRelevanceScore(product, queryTerms);
      const matchedFields = this.getMatchedFields(product, queryTerms);
      const highlights = this.generateHighlights(product, queryTerms);

      return {
        product,
        score,
        matchedFields,
        highlights,
      };
    }).filter(result => result.score > 0);
  }

  private calculateRelevanceScore(product: Product, queryTerms: string[]): number {
    let score = 0;
    const weights = {
      name: 3.0,
      description: 1.0,
      category: 2.0,
      brand: 2.5,
    };

    queryTerms.forEach(term => {
      // Name matching
      if (product.name.toLowerCase().includes(term)) {
        score += weights.name;
        if (product.name.toLowerCase().startsWith(term)) {
          score += 1.0; // Bonus for prefix match
        }
      }

      // Description matching
      if (product.description.toLowerCase().includes(term)) {
        score += weights.description;
      }

      // Category matching
      if (product.category.toLowerCase().includes(term)) {
        score += weights.category;
      }

      // Brand matching (if available)
      const brand = product.metadata?.brand?.toLowerCase() || '';
      if (brand.includes(term)) {
        score += weights.brand;
      }
    });

    // Boost score based on product popularity
    score *= (1 + (product.rating - 3) * 0.1); // Rating boost
    score *= (1 + Math.log(product.reviewCount + 1) * 0.05); // Review count boost

    return score;
  }

  private getMatchedFields(product: Product, queryTerms: string[]): string[] {
    const matchedFields: string[] = [];

    queryTerms.forEach(term => {
      if (product.name.toLowerCase().includes(term)) {
        matchedFields.push('name');
      }
      if (product.description.toLowerCase().includes(term)) {
        matchedFields.push('description');
      }
      if (product.category.toLowerCase().includes(term)) {
        matchedFields.push('category');
      }
    });

    return [...new Set(matchedFields)]; // Remove duplicates
  }

  private generateHighlights(product: Product, queryTerms: string[]): Record<string, string> {
    const highlights: Record<string, string> = {};

    queryTerms.forEach(term => {
      const regex = new RegExp(`(${term})`, 'gi');
      
      if (product.name.toLowerCase().includes(term)) {
        highlights.name = product.name.replace(regex, '<mark>$1</mark>');
      }
      
      if (product.description.toLowerCase().includes(term)) {
        const snippet = this.extractSnippet(product.description, term, 100);
        highlights.description = snippet.replace(regex, '<mark>$1</mark>');
      }
    });

    return highlights;
  }

  private extractSnippet(text: string, term: string, maxLength: number): string {
    const index = text.toLowerCase().indexOf(term.toLowerCase());
    if (index === -1) return text.substring(0, maxLength);

    const start = Math.max(0, index - maxLength / 2);
    const end = Math.min(text.length, start + maxLength);
    
    let snippet = text.substring(start, end);
    if (start > 0) snippet = '...' + snippet;
    if (end < text.length) snippet = snippet + '...';
    
    return snippet;
  }

  private sortResults(results: SearchResult[], sortBy: string): SearchResult[] {
    switch (sortBy) {
      case 'price_low':
        return results.sort((a, b) => a.product.price - b.product.price);
      case 'price_high':
        return results.sort((a, b) => b.product.price - a.product.price);
      case 'rating':
        return results.sort((a, b) => b.product.rating - a.product.rating);
      case 'newest':
        return results.sort((a, b) => 
          new Date(b.product.createdAt || '').getTime() - new Date(a.product.createdAt || '').getTime()
        );
      case 'popularity':
        return results.sort((a, b) => b.product.reviewCount - a.product.reviewCount);
      case 'relevance':
      default:
        return results.sort((a, b) => b.score - a.score);
    }
  }

  private getQueryCompletions(partialQuery: string): SearchSuggestion[] {
    const completions: SearchSuggestion[] = [];
    
    this.popularQueries.forEach((count, query) => {
      if (query.startsWith(partialQuery) && query !== partialQuery) {
        completions.push({
          text: query,
          type: 'query',
          score: count * 0.1,
        });
      }
    });

    return completions.slice(0, 5);
  }

  private getProductSuggestions(partialQuery: string, products: Product[]): SearchSuggestion[] {
    const suggestions: SearchSuggestion[] = [];
    
    products.forEach(product => {
      if (product.name.toLowerCase().includes(partialQuery)) {
        suggestions.push({
          text: product.name,
          type: 'product',
          score: product.rating * 0.2,
          metadata: { productId: product.id },
        });
      }
    });

    return suggestions.slice(0, 3);
  }

  private getCategorySuggestions(partialQuery: string, products: Product[]): SearchSuggestion[] {
    const categories = new Set<string>();
    
    products.forEach(product => {
      if (product.category.toLowerCase().includes(partialQuery)) {
        categories.add(product.category);
      }
    });

    return Array.from(categories).map(category => ({
      text: category,
      type: 'category' as const,
      score: 0.5,
    }));
  }

  private getBrandSuggestions(partialQuery: string, products: Product[]): SearchSuggestion[] {
    const brands = new Set<string>();
    
    products.forEach(product => {
      const brand = product.metadata?.brand;
      if (brand && brand.toLowerCase().includes(partialQuery)) {
        brands.add(brand);
      }
    });

    return Array.from(brands).map(brand => ({
      text: brand,
      type: 'brand' as const,
      score: 0.4,
    }));
  }

  private getPopularSuggestions(limit: number): SearchSuggestion[] {
    return Array.from(this.popularQueries.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([query, count]) => ({
        text: query,
        type: 'query' as const,
        score: count * 0.1,
      }));
  }

  private getPriceRange(price: number): string {
    if (price < 25) return '$0 - $25';
    if (price < 50) return '$25 - $50';
    if (price < 100) return '$50 - $100';
    if (price < 200) return '$100 - $200';
    if (price < 500) return '$200 - $500';
    return '$500+';
  }

  private updateSearchAnalytics(query: string, resultsCount: number): void {
    // Update the last search with results count
    if (this.searchHistory.length > 0) {
      const lastSearch = this.searchHistory[this.searchHistory.length - 1];
      if (lastSearch.query === query) {
        lastSearch.resultsCount = resultsCount;
      }
    }
  }

  private initializeSearchSuggestions(): void {
    // Initialize with some default popular searches
    this.popularQueries.set('electronics', 10);
    this.popularQueries.set('phone', 8);
    this.popularQueries.set('laptop', 7);
    this.popularQueries.set('headphones', 6);
    this.popularQueries.set('camera', 5);
  }

  // Storage
  private async loadStoredData(): Promise<void> {
    try {
      const [historyData, popularData] = await Promise.all([
        storage.getItem('searchHistory'),
        storage.getItem('popularQueries'),
      ]);

      if (historyData) {
        this.searchHistory = JSON.parse(historyData);
      }

      if (popularData) {
        const popularArray = JSON.parse(popularData);
        this.popularQueries = new Map(popularArray);
      }
    } catch (error) {
      console.error('Error loading search data:', error);
    }
  }

  private async saveData(): Promise<void> {
    try {
      const popularArray = Array.from(this.popularQueries.entries());
      
      await Promise.all([
        storage.setItem('searchHistory', JSON.stringify(this.searchHistory)),
        storage.setItem('popularQueries', JSON.stringify(popularArray)),
      ]);
    } catch (error) {
      console.error('Error saving search data:', error);
    }
  }

  // Public API
  public getSearchHistory(userId?: string): SearchQuery[] {
    if (userId) {
      return this.searchHistory.filter(search => search.userId === userId);
    }
    return [...this.searchHistory];
  }

  public clearSearchHistory(): void {
    this.searchHistory = [];
    this.saveData();
  }

  public recordSearchClick(searchId: string, productId: string): void {
    const search = this.searchHistory.find(s => s.id === searchId);
    if (search) {
      search.clickedResults.push(productId);
      this.saveData();
    }
  }
}

export default AdvancedSearchService.getInstance();
