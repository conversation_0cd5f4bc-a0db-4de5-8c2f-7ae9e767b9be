import * as LocalAuthentication from 'expo-local-authentication';
import * as Crypto from 'expo-crypto';
import { EventEmitter } from '../utils/EventEmitter';
import { storage } from '../utils/storage';

export interface BiometricConfig {
  enabled: boolean;
  supportedTypes: LocalAuthentication.AuthenticationType[];
  enrolledLevel: LocalAuthentication.SecurityLevel;
}

export interface TwoFactorConfig {
  enabled: boolean;
  method: 'sms' | 'email' | 'authenticator';
  backupCodes: string[];
  lastUsed?: string;
}

export interface SecuritySettings {
  biometric: BiometricConfig;
  twoFactor: TwoFactorConfig;
  sessionTimeout: number; // minutes
  maxLoginAttempts: number;
  requirePasswordChange: boolean;
  passwordChangeInterval: number; // days
  encryptionEnabled: boolean;
  fraudDetection: boolean;
}

export interface LoginAttempt {
  timestamp: string;
  success: boolean;
  ipAddress?: string;
  userAgent?: string;
  location?: string;
  failureReason?: string;
}

export interface SecurityEvent {
  id: string;
  type: 'login' | 'logout' | 'password_change' | 'biometric_auth' | '2fa_auth' | 'suspicious_activity';
  timestamp: string;
  userId: string;
  details: any;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

class SecurityService extends EventEmitter {
  private static instance: SecurityService;
  private securitySettings: SecuritySettings | null = null;
  private loginAttempts: Map<string, LoginAttempt[]> = new Map();
  private securityEvents: SecurityEvent[] = [];
  private sessionStartTime: Date | null = null;

  private constructor() {
    super();
    this.loadSecuritySettings();
  }

  public static getInstance(): SecurityService {
    if (!SecurityService.instance) {
      SecurityService.instance = new SecurityService();
    }
    return SecurityService.instance;
  }

  // Biometric Authentication
  public async checkBiometricSupport(): Promise<BiometricConfig> {
    try {
      const isAvailable = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      const securityLevel = await LocalAuthentication.getEnrolledLevelAsync();

      return {
        enabled: isAvailable && isEnrolled,
        supportedTypes,
        enrolledLevel: securityLevel,
      };
    } catch (error) {
      console.error('Error checking biometric support:', error);
      return {
        enabled: false,
        supportedTypes: [],
        enrolledLevel: LocalAuthentication.SecurityLevel.NONE,
      };
    }
  }

  public async authenticateWithBiometrics(
    promptMessage: string = 'Authenticate to continue'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const biometricConfig = await this.checkBiometricSupport();
      
      if (!biometricConfig.enabled) {
        return { success: false, error: 'Biometric authentication not available' };
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage,
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use Password',
        disableDeviceFallback: false,
      });

      if (result.success) {
        await this.logSecurityEvent('biometric_auth', 'Biometric authentication successful', 'low');
        return { success: true };
      } else {
        await this.logSecurityEvent('biometric_auth', `Biometric authentication failed: ${result.error}`, 'medium');
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Biometric authentication error:', error);
      return { success: false, error: 'Authentication failed' };
    }
  }

  // Two-Factor Authentication
  public async enableTwoFactor(method: 'sms' | 'email' | 'authenticator', contact?: string): Promise<{
    success: boolean;
    secret?: string;
    qrCode?: string;
    backupCodes?: string[];
  }> {
    try {
      // Generate backup codes
      const backupCodes = await this.generateBackupCodes();
      
      let secret: string | undefined;
      let qrCode: string | undefined;

      if (method === 'authenticator') {
        // Generate TOTP secret
        secret = await this.generateTOTPSecret();
        qrCode = this.generateQRCode(secret);
      }

      // Update security settings
      const settings = await this.getSecuritySettings();
      settings.twoFactor = {
        enabled: true,
        method,
        backupCodes,
      };

      await this.saveSecuritySettings(settings);
      await this.logSecurityEvent('2fa_auth', `Two-factor authentication enabled: ${method}`, 'medium');

      return {
        success: true,
        secret,
        qrCode,
        backupCodes,
      };
    } catch (error) {
      console.error('Error enabling 2FA:', error);
      return { success: false };
    }
  }

  public async verifyTwoFactor(code: string, isBackupCode: boolean = false): Promise<boolean> {
    try {
      const settings = await this.getSecuritySettings();
      
      if (!settings.twoFactor.enabled) {
        return false;
      }

      if (isBackupCode) {
        const codeIndex = settings.twoFactor.backupCodes.indexOf(code);
        if (codeIndex !== -1) {
          // Remove used backup code
          settings.twoFactor.backupCodes.splice(codeIndex, 1);
          await this.saveSecuritySettings(settings);
          await this.logSecurityEvent('2fa_auth', 'Backup code used for 2FA', 'medium');
          return true;
        }
        return false;
      }

      // Verify TOTP code (simplified - in real app would use proper TOTP library)
      const isValid = await this.verifyTOTPCode(code);
      
      if (isValid) {
        settings.twoFactor.lastUsed = new Date().toISOString();
        await this.saveSecuritySettings(settings);
        await this.logSecurityEvent('2fa_auth', '2FA verification successful', 'low');
      } else {
        await this.logSecurityEvent('2fa_auth', '2FA verification failed', 'medium');
      }

      return isValid;
    } catch (error) {
      console.error('Error verifying 2FA:', error);
      return false;
    }
  }

  // Session Management
  public startSession(): void {
    this.sessionStartTime = new Date();
  }

  public async checkSessionTimeout(): Promise<boolean> {
    if (!this.sessionStartTime) return false;

    const settings = await this.getSecuritySettings();
    const timeoutMinutes = settings.sessionTimeout;
    const now = new Date();
    const sessionDuration = (now.getTime() - this.sessionStartTime.getTime()) / (1000 * 60);

    return sessionDuration > timeoutMinutes;
  }

  public endSession(): void {
    this.sessionStartTime = null;
  }

  // Login Attempt Tracking
  public async recordLoginAttempt(
    userId: string,
    success: boolean,
    failureReason?: string
  ): Promise<void> {
    const attempt: LoginAttempt = {
      timestamp: new Date().toISOString(),
      success,
      failureReason,
    };

    const userAttempts = this.loginAttempts.get(userId) || [];
    userAttempts.push(attempt);

    // Keep only last 10 attempts
    if (userAttempts.length > 10) {
      userAttempts.splice(0, userAttempts.length - 10);
    }

    this.loginAttempts.set(userId, userAttempts);

    // Check for suspicious activity
    await this.checkSuspiciousActivity(userId, userAttempts);
  }

  public async isAccountLocked(userId: string): Promise<boolean> {
    const settings = await this.getSecuritySettings();
    const userAttempts = this.loginAttempts.get(userId) || [];
    
    // Check recent failed attempts
    const recentAttempts = userAttempts.filter(attempt => {
      const attemptTime = new Date(attempt.timestamp);
      const now = new Date();
      const timeDiff = (now.getTime() - attemptTime.getTime()) / (1000 * 60); // minutes
      return timeDiff <= 15 && !attempt.success; // Last 15 minutes
    });

    return recentAttempts.length >= settings.maxLoginAttempts;
  }

  // Fraud Detection
  private async checkSuspiciousActivity(userId: string, attempts: LoginAttempt[]): Promise<void> {
    const settings = await this.getSecuritySettings();
    if (!settings.fraudDetection) return;

    // Check for multiple failed attempts
    const recentFailures = attempts.filter(attempt => {
      const attemptTime = new Date(attempt.timestamp);
      const now = new Date();
      const timeDiff = (now.getTime() - attemptTime.getTime()) / (1000 * 60);
      return timeDiff <= 30 && !attempt.success;
    });

    if (recentFailures.length >= 3) {
      await this.logSecurityEvent(
        'suspicious_activity',
        `Multiple failed login attempts detected for user ${userId}`,
        'high'
      );
      this.emit('suspiciousActivity', { userId, type: 'multiple_failed_logins', attempts: recentFailures });
    }
  }

  // Data Encryption
  public async encryptData(data: string): Promise<string> {
    try {
      const settings = await this.getSecuritySettings();
      if (!settings.encryptionEnabled) return data;

      // Simple encryption (in production, use proper encryption library)
      const digest = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        data + 'encryption_key'
      );
      return digest;
    } catch (error) {
      console.error('Encryption error:', error);
      return data;
    }
  }

  public async decryptData(encryptedData: string): Promise<string> {
    // Simplified decryption - in production use proper decryption
    return encryptedData;
  }

  // Security Events
  private async logSecurityEvent(
    type: SecurityEvent['type'],
    details: any,
    severity: SecurityEvent['severity'],
    userId: string = 'unknown'
  ): Promise<void> {
    const event: SecurityEvent = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      type,
      timestamp: new Date().toISOString(),
      userId,
      details,
      severity,
    };

    this.securityEvents.push(event);

    // Keep only last 100 events
    if (this.securityEvents.length > 100) {
      this.securityEvents.splice(0, this.securityEvents.length - 100);
    }

    // Emit event for real-time monitoring
    this.emit('securityEvent', event);

    // Save to storage
    await storage.setItem('securityEvents', JSON.stringify(this.securityEvents));
  }

  public getSecurityEvents(): SecurityEvent[] {
    return [...this.securityEvents];
  }

  // Helper Methods
  private async generateBackupCodes(): Promise<string[]> {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      const code = Math.random().toString(36).substr(2, 8).toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  private async generateTOTPSecret(): Promise<string> {
    // Generate a random secret for TOTP
    return Math.random().toString(36).substr(2, 32).toUpperCase();
  }

  private generateQRCode(secret: string): string {
    // Generate QR code URL for authenticator apps
    const appName = 'VendorHub';
    const issuer = 'VendorHub';
    return `otpauth://totp/${appName}?secret=${secret}&issuer=${issuer}`;
  }

  private async verifyTOTPCode(code: string): Promise<boolean> {
    // Simplified TOTP verification - in production use proper TOTP library
    return code.length === 6 && /^\d+$/.test(code);
  }

  // Settings Management
  private async loadSecuritySettings(): Promise<void> {
    try {
      const stored = await storage.getItem('securitySettings');
      if (stored) {
        this.securitySettings = JSON.parse(stored);
      } else {
        this.securitySettings = this.getDefaultSecuritySettings();
        await this.saveSecuritySettings(this.securitySettings);
      }

      // Load security events
      const eventsData = await storage.getItem('securityEvents');
      if (eventsData) {
        this.securityEvents = JSON.parse(eventsData);
      }
    } catch (error) {
      console.error('Error loading security settings:', error);
      this.securitySettings = this.getDefaultSecuritySettings();
    }
  }

  private async saveSecuritySettings(settings: SecuritySettings): Promise<void> {
    try {
      this.securitySettings = settings;
      await storage.setItem('securitySettings', JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving security settings:', error);
    }
  }

  public async getSecuritySettings(): Promise<SecuritySettings> {
    if (!this.securitySettings) {
      await this.loadSecuritySettings();
    }
    return this.securitySettings!;
  }

  private getDefaultSecuritySettings(): SecuritySettings {
    return {
      biometric: {
        enabled: false,
        supportedTypes: [],
        enrolledLevel: LocalAuthentication.SecurityLevel.NONE,
      },
      twoFactor: {
        enabled: false,
        method: 'email',
        backupCodes: [],
      },
      sessionTimeout: 30, // 30 minutes
      maxLoginAttempts: 5,
      requirePasswordChange: false,
      passwordChangeInterval: 90, // 90 days
      encryptionEnabled: true,
      fraudDetection: true,
    };
  }
}

export default SecurityService.getInstance();
