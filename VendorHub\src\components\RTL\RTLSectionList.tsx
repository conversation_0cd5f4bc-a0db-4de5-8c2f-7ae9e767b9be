import React, { useMemo } from 'react';
import { SectionList, SectionListProps, StyleSheet, ViewStyle } from 'react-native';
import { useI18n } from '../../hooks/useI18n';

interface RTLSectionListProps<T, S> extends SectionListProps<T, S> {
  style?: ViewStyle | ViewStyle[];
  contentContainerStyle?: ViewStyle | ViewStyle[];
}

export const RTLSectionList = <T, S = any>({
  style,
  contentContainerStyle,
  horizontal,
  children,
  ...props
}: RTLSectionListProps<T, S>) => {
  const { isRTL } = useI18n();

  const rtlStyle = useMemo(() => {
    if (!style || !isRTL) return style;
    
    const flattenedStyle = StyleSheet.flatten(style);
    const rtlFlattenedStyle = { ...flattenedStyle };
    
    // Flip padding
    if (flattenedStyle.paddingLeft !== undefined) {
      rtlFlattenedStyle.paddingRight = flattenedStyle.paddingLeft;
      delete rtlFlattenedStyle.paddingLeft;
    }
    if (flattenedStyle.paddingRight !== undefined) {
      rtlFlattenedStyle.paddingLeft = flattenedStyle.paddingRight;
      delete rtlFlattenedStyle.paddingRight;
    }
    
    // Flip margin
    if (flattenedStyle.marginLeft !== undefined) {
      rtlFlattenedStyle.marginRight = flattenedStyle.marginLeft;
      delete rtlFlattenedStyle.marginLeft;
    }
    if (flattenedStyle.marginRight !== undefined) {
      rtlFlattenedStyle.marginLeft = flattenedStyle.marginRight;
      delete rtlFlattenedStyle.marginRight;
    }
    
    return rtlFlattenedStyle;
  }, [style, isRTL]);

  const rtlContentContainerStyle = useMemo(() => {
    if (!contentContainerStyle || !isRTL) return contentContainerStyle;
    
    const flattenedStyle = StyleSheet.flatten(contentContainerStyle);
    const rtlFlattenedStyle = { ...flattenedStyle };
    
    // Flip flex direction for horizontal scrolling
    if (horizontal && flattenedStyle.flexDirection === 'row') {
      rtlFlattenedStyle.flexDirection = 'row-reverse';
    } else if (horizontal && flattenedStyle.flexDirection === 'row-reverse') {
      rtlFlattenedStyle.flexDirection = 'row';
    }
    
    // Flip padding
    if (flattenedStyle.paddingLeft !== undefined) {
      rtlFlattenedStyle.paddingRight = flattenedStyle.paddingLeft;
      delete rtlFlattenedStyle.paddingLeft;
    }
    if (flattenedStyle.paddingRight !== undefined) {
      rtlFlattenedStyle.paddingLeft = flattenedStyle.paddingRight;
      delete rtlFlattenedStyle.paddingRight;
    }
    
    return rtlFlattenedStyle;
  }, [contentContainerStyle, isRTL, horizontal]);
  
  return (
    <SectionList
      style={rtlStyle}
      contentContainerStyle={rtlContentContainerStyle}
      horizontal={horizontal}
      {...props}
    >
      {children}
    </SectionList>
  );
};
