import { useMemo } from 'react';
import { TextStyle } from 'react-native';
import { useI18n } from './useI18n';
import FontService, { FontConfig } from '../services/FontService';

export interface FontStyleOptions {
  weight?: keyof FontConfig;
  size?: number;
  color?: string;
  lineHeight?: number;
  textAlign?: 'left' | 'right' | 'center' | 'auto';
}

export const useFont = (options: FontStyleOptions = {}): TextStyle => {
  const { currentLanguage, isRTL } = useI18n();
  const fontService = FontService;

  const fontStyle = useMemo(() => {
    const {
      weight = 'regular',
      size,
      color,
      lineHeight,
      textAlign,
    } = options;

    const baseStyle = fontService.getTextStyle(currentLanguage, weight);
    
    const style: TextStyle = {
      ...baseStyle,
    };

    // Apply custom size if provided
    if (size) {
      style.fontSize = size;
    }

    // Apply custom color if provided
    if (color) {
      style.color = color;
    }

    // Apply custom line height if provided
    if (lineHeight) {
      style.lineHeight = lineHeight;
    }

    // Apply custom text alignment if provided
    if (textAlign) {
      if (textAlign === 'auto') {
        // Use default RTL/LTR alignment
        style.textAlign = isRTL ? 'right' : 'left';
      } else {
        style.textAlign = textAlign;
      }
    }

    return style;
  }, [currentLanguage, isRTL, options]);

  return fontStyle;
};

export const useArabicFont = (options: FontStyleOptions = {}): TextStyle => {
  const fontService = FontService;

  const fontStyle = useMemo(() => {
    const {
      weight = 'regular',
      size,
      color,
      lineHeight,
      textAlign = 'right',
    } = options;

    const baseStyle = fontService.getArabicTextStyle(weight);
    
    const style: TextStyle = {
      ...baseStyle,
      textAlign,
    };

    if (size) style.fontSize = size;
    if (color) style.color = color;
    if (lineHeight) style.lineHeight = lineHeight;

    return style;
  }, [options]);

  return fontStyle;
};

export const useEnglishFont = (options: FontStyleOptions = {}): TextStyle => {
  const fontService = FontService;

  const fontStyle = useMemo(() => {
    const {
      weight = 'regular',
      size,
      color,
      lineHeight,
      textAlign = 'left',
    } = options;

    const baseStyle = fontService.getEnglishTextStyle(weight);
    
    const style: TextStyle = {
      ...baseStyle,
      textAlign,
    };

    if (size) style.fontSize = size;
    if (color) style.color = color;
    if (lineHeight) style.lineHeight = lineHeight;

    return style;
  }, [options]);

  return fontStyle;
};
