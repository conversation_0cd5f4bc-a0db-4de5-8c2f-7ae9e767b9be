# React Native Multi-Vendor E-commerce App Creation Prompt

Create a comprehensive multi-vendor e-commerce mobile application using React Native with the following specifications:

## 🎯 **Core Requirements**

### **Platform Overview**
- Build a multi-vendor marketplace mobile app where I (as platform owner) can host multiple vendors
- Each vendor gets their own shop/storefront within the app
- Vendors can register but require admin approval before being activated
- Create separate navigation flows for admin and vendors with role-based access
- Native mobile experience with smooth animations and gestures

### **Admin Features**
- **Admin Login System**: Default credentials (<EMAIL> / admin123)
- **Vendor Management Screen**: 
  - FlatList/SectionList showing all registered vendors with status badges
  - Swipe actions or buttons to approve/reject vendor applications
  - Pull-to-refresh functionality
  - Search and filter vendors
- **Platform Analytics Dashboard**:
  - Animated counter components for statistics
  - Card-based metrics display
  - Charts/graphs for visual data representation
  - Real-time updates
- **Product Oversight**: Grid view of all products with vendor attribution
- **Order Management**: List view with order status management

### **Vendor Features**
- **Registration Flow**: 
  - Multi-step form with React Native TextInput components
  - Form validation with real-time feedback
  - Image picker for business logo/photos
  - Success confirmation screen
- **Vendor Dashboard** (only accessible after approval):
  - Tab navigation for different sections
  - Animated statistics cards
  - Quick action buttons for common tasks
  - Revenue charts and analytics
- **Product Management**:
  - Add product form with image selection
  - Product gallery with swipeable cards
  - Inventory management with stepper controls
  - Categories picker with modal selection
  - Product editing capabilities

### **Shop System**
- **Individual Vendor Shops**: Dedicated shop screens for each vendor
- **Shop Browser**: Horizontal scrollable vendor cards + grid view
- **Product Catalog**: Native grid/list toggle views
- **Search Functionality**: Real-time search with filtering
- **Vendor Profiles**: Detailed vendor information screens

### **Order System**
- **Shopping Cart**: Add to cart functionality with badge indicators
- **Checkout Flow**: Multi-step checkout process
- **Order Tracking**: Timeline view for order status
- **Push Notifications**: Order updates and confirmations
- **Order History**: Grouped by date with expandable sections

## 🎨 **Design Requirements**

### **Visual Design**
- **Modern Mobile UI**: Follow iOS/Android design guidelines
- **Color Scheme**: Primary gradient theme (#667eea to #764ba2)
- **Glassmorphism Effects**: 
  - Blurred backgrounds using BlurView
  - Semi-transparent overlays
  - Subtle shadow effects
- **Animations**:
  - React Native Animated API for smooth transitions
  - Spring animations for interactions
  - Fade/slide transitions between screens
  - Loading animations and skeletons

### **Navigation Structure**
- **React Navigation v6**: Stack, Tab, and Drawer navigators
- **Authentication Flow**: Separate auth stack with login/register
- **Admin Navigation**: 
  - Bottom tabs: Dashboard, Vendors, Products, Orders
  - Stack navigation for detailed views
- **Vendor Navigation**:
  - Bottom tabs: Overview, Products, Orders, Shop
  - Modal presentations for forms
- **Public Navigation**: 
  - Stack navigation: Home, Shops, Product Details, Cart

### **UI Components**
- **Custom Components**:
  - Reusable Card component with shadows
  - Statistics Counter with animation
  - Status Badge with color coding
  - Custom Button with loading states
  - Form Input with validation styling
- **Native Components**:
  - FlatList for performant scrolling
  - Modal for overlays and forms
  - ActionSheet for contextual actions
  - RefreshControl for pull-to-refresh

## 🛠 **Technical Specifications**

### **Technology Stack**
- **React Native CLI** or **Expo** setup
- **React Navigation 6** for navigation
- **React Native Vector Icons** for iconography
- **React Native Gesture Handler** for interactions
- **React Native Reanimated** for complex animations
- **React Hook Form** for form management
- **Context API** or **Redux Toolkit** for state management

### **Project Structure**
```
src/
├── components/          # Reusable UI components
├── screens/            # Screen components
│   ├── auth/          # Authentication screens
│   ├── admin/         # Admin dashboard screens
│   ├── vendor/        # Vendor dashboard screens
│   └── public/        # Public shopping screens
├── navigation/         # Navigation configuration
├── hooks/             # Custom React hooks
├── utils/             # Utility functions
├── constants/         # App constants and theme
└── data/              # Sample data and mock API
```

### **State Management**
- **Context Providers**: 
  - AuthContext for user authentication
  - DataContext for vendors/products/orders
  - ThemeContext for styling consistency
- **Custom Hooks**:
  - useAuth for authentication logic
  - useVendors for vendor management
  - useProducts for product operations
  - useOrders for order handling

### **Data Structure**
Create sample data with:
- **Vendors**: Array with approved/pending/rejected statuses
- **Products**: Nested under vendors with categories and inventory
- **Orders**: Order history with status tracking
- **Users**: Admin and vendor user objects

## 📱 **Mobile-Specific Features**

### **Native Functionality**
- **Image Picker**: Camera and gallery access for product photos
- **Biometric Authentication**: Face ID/Touch ID for secure login
- **Push Notifications**: Order updates and system notifications
- **Deep Linking**: Direct navigation to specific products/shops
- **Offline Support**: Basic caching for product viewing

### **Gestures & Interactions**
- **Swipe Actions**: Swipe to approve/reject vendors
- **Pull-to-Refresh**: Update data in lists
- **Long Press**: Context menus for quick actions
- **Pinch-to-Zoom**: Product image galleries
- **Haptic Feedback**: Tactile responses for interactions

### **Performance Optimizations**
- **FlatList Optimization**: 
  - getItemLayout for known dimensions
  - keyExtractor for efficient re-renders
  - removeClippedSubviews for memory management
- **Image Optimization**:
  - Lazy loading for product images
  - Image caching strategies
  - Placeholder components
- **Code Splitting**: Lazy load screens with React.lazy

## 🎨 **Screen Designs**

### **Authentication Screens**
- **Welcome Screen**: App intro with role selection (Admin/Vendor/Customer)
- **Login Screen**: Form with animated inputs and biometric option
- **Vendor Registration**: Multi-step wizard with progress indicator
- **Approval Pending**: Status screen for pending vendor accounts

### **Admin Dashboard**
- **Overview**: Statistics cards with charts and quick actions
- **Vendor Management**: List with filter chips and action buttons
- **Product Gallery**: Grid view with vendor tags and actions
- **Order Dashboard**: Grouped lists with status indicators

### **Vendor Dashboard**
- **Analytics**: Revenue charts and key metrics
- **Product Manager**: Grid with floating action button to add
- **Order Fulfillment**: List with quick status update actions
- **Shop Preview**: Live preview of vendor's public shop

### **Public Shopping App**
- **Home**: Featured vendors and products with search
- **Shop Browser**: Horizontal scrolling vendor cards
- **Product Details**: Image carousel with purchase options
- **Shopping Cart**: List with quantity controls and checkout

## 🔧 **Implementation Guidelines**

### **Component Architecture**
- **Functional Components**: Use hooks throughout
- **TypeScript**: Strong typing for props and state
- **Custom Hooks**: Extract reusable logic
- **Error Boundaries**: Graceful error handling
- **Testing**: Unit tests for utilities and components

### **Styling Approach**
- **StyleSheet.create**: Optimized styling
- **Theme System**: Consistent colors, fonts, spacing
- **Responsive Design**: Adapt to different screen sizes
- **Dark Mode Support**: Toggle between light/dark themes
- **Platform-Specific Styles**: iOS vs Android differences

### **Navigation Setup**
```javascript
// Example navigation structure
const AuthStack = () => (
  <Stack.Navigator>
    <Stack.Screen name="Welcome" component={WelcomeScreen} />
    <Stack.Screen name="Login" component={LoginScreen} />
    <Stack.Screen name="Register" component={RegisterScreen} />
  </Stack.Navigator>
);

const AdminTabs = () => (
  <Tab.Navigator>
    <Tab.Screen name="Dashboard" component={AdminDashboard} />
    <Tab.Screen name="Vendors" component={VendorManagement} />
    <Tab.Screen name="Products" component={ProductOverview} />
    <Tab.Screen name="Orders" component={OrderManagement} />
  </Tab.Navigator>
);
```

### **Sample Data Integration**
- Mock API functions that simulate server responses
- Realistic data with proper relationships
- Error simulation for robust testing
- Pagination support for large datasets

## 🚀 **Key Features to Implement**

### **Authentication Flow**
- Role-based login with persistent sessions
- Registration with email validation
- Password reset functionality
- Biometric authentication setup

### **Admin Capabilities**
- Vendor approval workflow with notifications
- Platform analytics with data visualization
- Global product and order management
- System settings and configuration

### **Vendor Tools**
- Product catalog management with image upload
- Order processing with status updates
- Sales analytics and reporting
- Shop customization options

### **Shopping Experience**
- Product browsing with search and filters
- Shopping cart with persistent state
- Checkout process with order confirmation
- Order tracking and history

## 🎯 **Success Criteria**

The final React Native application should:

1. **Authentication**: Smooth login flows for different user types
2. **Admin Control**: Complete vendor and platform management
3. **Vendor Operations**: Full shop and product management capabilities
4. **Shopping Experience**: Intuitive product discovery and purchasing
5. **Performance**: 60fps animations and smooth scrolling
6. **Native Feel**: Platform-appropriate UI patterns and interactions
7. **Responsive**: Works across phone and tablet screen sizes
8. **Scalable**: Clean architecture ready for real backend integration

**App Name**: "VendorHub" - Multi-Vendor Mobile Marketplace

Create this as a complete React Native project with proper file structure, navigation setup, and all screens implemented with sample data. The app should demonstrate native mobile best practices while providing a comprehensive multi-vendor e-commerce platform experience.