import { useMemo } from 'react';
import { useData } from '../contexts/DataContext';
import { PRODUCT_CATEGORIES } from '../constants';
import type { Product, ProductCategory } from '../contexts/DataContext';

export const useProducts = () => {
  const {
    products,
    addProduct,
    updateProduct,
    deleteProduct,
    getProductById,
    searchProducts,
    getProductsByCategory,
    isLoading,
    error,
  } = useData();

  // Memoized product statistics
  const productStats = useMemo(() => {
    const total = products.length;
    const active = products.filter(p => p.isActive).length;
    const inactive = products.filter(p => !p.isActive).length;
    const outOfStock = products.filter(p => p.inventory === 0).length;
    const lowStock = products.filter(p => p.inventory > 0 && p.inventory <= 10).length;

    const averagePrice = products.length > 0 
      ? products.reduce((sum, p) => sum + p.price, 0) / products.length 
      : 0;

    const averageRating = products.length > 0
      ? products.reduce((sum, p) => sum + p.rating, 0) / products.length
      : 0;

    return {
      total,
      active,
      inactive,
      outOfStock,
      lowStock,
      averagePrice,
      averageRating,
      activationRate: total > 0 ? (active / total) * 100 : 0,
    };
  }, [products]);

  // Get products by vendor
  const getProductsByVendor = (vendorId: string): Product[] => {
    return products.filter(product => product.vendorId === vendorId);
  };

  // Get featured products (high rating and active)
  const getFeaturedProducts = (limit: number = 10): Product[] => {
    return products
      .filter(product => product.isActive && product.rating >= 4.0)
      .sort((a, b) => b.rating - a.rating)
      .slice(0, limit);
  };

  // Get best selling products (by review count as proxy)
  const getBestSellingProducts = (limit: number = 10): Product[] => {
    return products
      .filter(product => product.isActive)
      .sort((a, b) => b.reviewCount - a.reviewCount)
      .slice(0, limit);
  };

  // Get products on sale
  const getProductsOnSale = (): Product[] => {
    return products.filter(product => 
      product.isActive && 
      product.originalPrice && 
      product.originalPrice > product.price
    );
  };

  // Get low stock products
  const getLowStockProducts = (threshold: number = 10): Product[] => {
    return products.filter(product => 
      product.isActive && 
      product.inventory <= threshold && 
      product.inventory > 0
    );
  };

  // Get out of stock products
  const getOutOfStockProducts = (): Product[] => {
    return products.filter(product => product.inventory === 0);
  };

  // Get products by price range
  const getProductsByPriceRange = (minPrice: number, maxPrice: number): Product[] => {
    return products.filter(product => 
      product.isActive && 
      product.price >= minPrice && 
      product.price <= maxPrice
    );
  };

  // Get products by rating
  const getProductsByRating = (minRating: number): Product[] => {
    return products.filter(product => 
      product.isActive && 
      product.rating >= minRating
    );
  };

  // Get category statistics
  const getCategoryStats = () => {
    const categoryStats = PRODUCT_CATEGORIES.map(category => {
      const categoryProducts = products.filter(p => p.category === category);
      const activeProducts = categoryProducts.filter(p => p.isActive);
      
      return {
        category,
        totalProducts: categoryProducts.length,
        activeProducts: activeProducts.length,
        averagePrice: activeProducts.length > 0 
          ? activeProducts.reduce((sum, p) => sum + p.price, 0) / activeProducts.length 
          : 0,
        averageRating: activeProducts.length > 0
          ? activeProducts.reduce((sum, p) => sum + p.rating, 0) / activeProducts.length
          : 0,
      };
    });

    return categoryStats.sort((a, b) => b.totalProducts - a.totalProducts);
  };

  // Advanced search with filters
  const searchProductsAdvanced = (filters: {
    query?: string;
    category?: ProductCategory;
    minPrice?: number;
    maxPrice?: number;
    minRating?: number;
    inStock?: boolean;
    vendorId?: string;
  }): Product[] => {
    let filteredProducts = products.filter(product => product.isActive);

    if (filters.query) {
      const lowercaseQuery = filters.query.toLowerCase();
      filteredProducts = filteredProducts.filter(product =>
        product.name.toLowerCase().includes(lowercaseQuery) ||
        product.description.toLowerCase().includes(lowercaseQuery) ||
        product.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
      );
    }

    if (filters.category) {
      filteredProducts = filteredProducts.filter(product => 
        product.category === filters.category
      );
    }

    if (filters.minPrice !== undefined) {
      filteredProducts = filteredProducts.filter(product => 
        product.price >= filters.minPrice!
      );
    }

    if (filters.maxPrice !== undefined) {
      filteredProducts = filteredProducts.filter(product => 
        product.price <= filters.maxPrice!
      );
    }

    if (filters.minRating !== undefined) {
      filteredProducts = filteredProducts.filter(product => 
        product.rating >= filters.minRating!
      );
    }

    if (filters.inStock) {
      filteredProducts = filteredProducts.filter(product => 
        product.inventory > 0
      );
    }

    if (filters.vendorId) {
      filteredProducts = filteredProducts.filter(product => 
        product.vendorId === filters.vendorId
      );
    }

    return filteredProducts;
  };

  // Sort products
  const sortProducts = (
    productList: Product[], 
    sortBy: 'name' | 'price' | 'rating' | 'createdAt' | 'inventory',
    order: 'asc' | 'desc' = 'asc'
  ): Product[] => {
    return [...productList].sort((a, b) => {
      let aValue: any = a[sortBy];
      let bValue: any = b[sortBy];

      if (sortBy === 'createdAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      if (aValue < bValue) return order === 'asc' ? -1 : 1;
      if (aValue > bValue) return order === 'asc' ? 1 : -1;
      return 0;
    });
  };

  // Get product recommendations (simple algorithm based on category and rating)
  const getProductRecommendations = (productId: string, limit: number = 5): Product[] => {
    const product = getProductById(productId);
    if (!product) return [];

    return products
      .filter(p => 
        p.id !== productId && 
        p.isActive && 
        p.category === product.category
      )
      .sort((a, b) => b.rating - a.rating)
      .slice(0, limit);
  };

  // Get all products
  const getAllProducts = () => products;

  return {
    // Data
    products,
    productStats,
    isLoading,
    error,

    // Methods
    addProduct,
    updateProduct,
    deleteProduct,
    getProductById,
    getAllProducts,
    searchProducts,
    searchProductsAdvanced,
    getProductsByCategory,
    getProductsByVendor,
    getFeaturedProducts,
    getBestSellingProducts,
    getProductsOnSale,
    getLowStockProducts,
    getOutOfStockProducts,
    getProductsByPriceRange,
    getProductsByRating,
    getCategoryStats,
    sortProducts,
    getProductRecommendations,
  };
};
