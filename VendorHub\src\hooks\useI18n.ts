import { useState, useEffect } from 'react';
import I18nService, { SupportedLanguage, LanguageConfig } from '../services/I18nService';

export const useI18n = () => {
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>(
    I18nService.getCurrentLanguage()
  );
  const [isRTL, setIsRTL] = useState(I18nService.isRTL());

  useEffect(() => {
    const handleLanguageChange = (language: SupportedLanguage) => {
      setCurrentLanguage(language);
      setIsRTL(I18nService.isRTL());
    };

    I18nService.on('languageChanged', handleLanguageChange);

    return () => {
      I18nService.removeListener('languageChanged', handleLanguageChange);
    };
  }, []);

  const changeLanguage = async (language: SupportedLanguage) => {
    await I18nService.setLanguage(language);
  };

  const t = (key: string, params?: Record<string, string | number>) => {
    return I18nService.t(key, params);
  };

  const formatCurrency = (amount: number) => {
    return I18nService.formatCurrency(amount);
  };

  const formatNumber = (number: number) => {
    return I18nService.formatNumber(number);
  };

  const formatDate = (date: Date | string) => {
    return I18nService.formatDate(date);
  };

  const formatRelativeTime = (date: Date | string) => {
    return I18nService.formatRelativeTime(date);
  };

  const getSupportedLanguages = (): LanguageConfig[] => {
    return I18nService.getSupportedLanguages();
  };

  const getCurrentLanguageConfig = (): LanguageConfig => {
    return I18nService.getCurrentLanguageConfig();
  };

  return {
    currentLanguage,
    isRTL,
    changeLanguage,
    t,
    formatCurrency,
    formatNumber,
    formatDate,
    formatRelativeTime,
    getSupportedLanguages,
    getCurrentLanguageConfig,
  };
};
