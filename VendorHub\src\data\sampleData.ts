import { VENDOR_STATUS, ORDER_STATUS, PRODUCT_CATEGORIES } from '../constants';
import type { Vendor, Product, Order } from '../contexts/DataContext';

// Sample Vendors
export const sampleVendors: Vendor[] = [
  {
    id: 'vendor-1',
    businessName: 'TechGear Pro',
    businessDescription: 'Premium electronics and gadgets for tech enthusiasts',
    ownerName: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0101',
    businessLogo: 'https://via.placeholder.com/150x150/667eea/ffffff?text=TG',
    status: VENDOR_STATUS.APPROVED,
    rating: 4.8,
    totalProducts: 25,
    totalOrders: 150,
    revenue: 16920, // ~45000 USD converted to BHD
    createdAt: '2024-01-15T10:00:00Z',
    approvedAt: '2024-01-16T14:30:00Z',
    address: {
      street: '123 Tech Street',
      city: 'San Francisco',
      state: 'CA',
      zipCode: '94105',
      country: 'USA',
    },
  },
  {
    id: 'vendor-2',
    businessName: 'Fashion Forward',
    businessDescription: 'Trendy clothing and accessories for modern lifestyle',
    ownerName: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-0102',
    businessLogo: 'https://via.placeholder.com/150x150/764ba2/ffffff?text=FF',
    status: VENDOR_STATUS.APPROVED,
    rating: 4.6,
    totalProducts: 40,
    totalOrders: 200,
    revenue: 12032, // ~32000 USD converted to BHD
    createdAt: '2024-01-20T09:15:00Z',
    approvedAt: '2024-01-21T11:45:00Z',
    address: {
      street: '456 Fashion Ave',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      country: 'USA',
    },
  },
  {
    id: 'vendor-3',
    businessName: 'Home & Garden Plus',
    businessDescription: 'Everything you need for your home and garden',
    ownerName: 'Mike Wilson',
    email: '<EMAIL>',
    phone: '******-0103',
    businessLogo: 'https://via.placeholder.com/150x150/4CAF50/ffffff?text=HG',
    status: VENDOR_STATUS.PENDING,
    rating: 0,
    totalProducts: 0,
    totalOrders: 0,
    revenue: 0,
    createdAt: '2024-02-01T16:20:00Z',
    address: {
      street: '789 Garden Lane',
      city: 'Austin',
      state: 'TX',
      zipCode: '73301',
      country: 'USA',
    },
  },
  {
    id: 'vendor-4',
    businessName: 'Sports Central',
    businessDescription: 'Professional sports equipment and outdoor gear',
    ownerName: 'Lisa Chen',
    email: '<EMAIL>',
    phone: '******-0104',
    businessLogo: 'https://via.placeholder.com/150x150/FF9800/ffffff?text=SC',
    status: VENDOR_STATUS.APPROVED,
    rating: 4.7,
    totalProducts: 35,
    totalOrders: 120,
    revenue: 10528, // ~28000 USD converted to BHD
    createdAt: '2024-01-25T13:10:00Z',
    approvedAt: '2024-01-26T09:20:00Z',
    address: {
      street: '321 Sports Blvd',
      city: 'Denver',
      state: 'CO',
      zipCode: '80202',
      country: 'USA',
    },
  },
  {
    id: 'vendor-5',
    businessName: 'BookWorm Paradise',
    businessDescription: 'Rare and popular books for all reading enthusiasts',
    ownerName: 'David Brown',
    email: '<EMAIL>',
    phone: '******-0105',
    businessLogo: 'https://via.placeholder.com/150x150/2196F3/ffffff?text=BP',
    status: VENDOR_STATUS.REJECTED,
    rating: 0,
    totalProducts: 0,
    totalOrders: 0,
    revenue: 0,
    createdAt: '2024-01-30T11:30:00Z',
    rejectedAt: '2024-02-02T15:45:00Z',
    address: {
      street: '654 Library St',
      city: 'Boston',
      state: 'MA',
      zipCode: '02101',
      country: 'USA',
    },
  },
];

// Sample Products
export const sampleProducts: Product[] = [
  // TechGear Pro Products
  {
    id: 'product-1',
    vendorId: 'vendor-1',
    name: 'Wireless Bluetooth Headphones',
    description: 'Premium noise-cancelling wireless headphones with 30-hour battery life',
    price: 75.200, // ~199.99 USD converted to BHD
    originalPrice: 94.000, // ~249.99 USD converted to BHD
    category: 'Electronics',
    images: [
      'https://via.placeholder.com/400x400/667eea/ffffff?text=Headphones',
      'https://via.placeholder.com/400x400/764ba2/ffffff?text=Side+View',
    ],
    inventory: 50,
    isActive: true,
    rating: 4.8,
    reviewCount: 124,
    tags: ['wireless', 'bluetooth', 'noise-cancelling', 'premium'],
    createdAt: '2024-01-16T10:00:00Z',
    updatedAt: '2024-02-01T14:30:00Z',
    specifications: {
      'Battery Life': '30 hours',
      'Connectivity': 'Bluetooth 5.0',
      'Weight': '250g',
      'Warranty': '2 years',
    },
  },
  {
    id: 'product-2',
    vendorId: 'vendor-1',
    name: 'Smart Watch Pro',
    description: 'Advanced fitness tracking smartwatch with heart rate monitor',
    price: 112.800, // ~299.99 USD converted to BHD
    category: 'Electronics',
    images: [
      'https://via.placeholder.com/400x400/667eea/ffffff?text=SmartWatch',
    ],
    inventory: 30,
    isActive: true,
    rating: 4.6,
    reviewCount: 89,
    tags: ['smartwatch', 'fitness', 'health', 'waterproof'],
    createdAt: '2024-01-18T12:00:00Z',
    updatedAt: '2024-01-25T16:45:00Z',
    specifications: {
      'Display': '1.4" AMOLED',
      'Battery': '7 days',
      'Water Resistance': '5ATM',
      'Sensors': 'Heart Rate, GPS, Accelerometer',
    },
  },
  {
    id: 'product-3',
    vendorId: 'vendor-1',
    name: 'Portable Phone Charger',
    description: 'High-capacity 20000mAh power bank with fast charging',
    price: 18.800, // ~49.99 USD converted to BHD
    originalPrice: 26.300, // ~69.99 USD converted to BHD
    category: 'Electronics',
    images: [
      'https://via.placeholder.com/400x400/667eea/ffffff?text=PowerBank',
    ],
    inventory: 0,
    isActive: true,
    rating: 4.4,
    reviewCount: 156,
    tags: ['powerbank', 'portable', 'fast-charging', 'travel'],
    createdAt: '2024-01-20T09:30:00Z',
    updatedAt: '2024-02-03T11:15:00Z',
    specifications: {
      'Capacity': '20000mAh',
      'Output': '18W Fast Charge',
      'Ports': '2x USB-A, 1x USB-C',
      'Weight': '400g',
    },
  },

  // Fashion Forward Products
  {
    id: 'product-4',
    vendorId: 'vendor-2',
    name: 'Designer Leather Jacket',
    description: 'Premium genuine leather jacket with modern cut',
    price: 112.800, // ~299.99 USD converted to BHD
    originalPrice: 150.400, // ~399.99 USD converted to BHD
    category: 'Fashion',
    images: [
      'https://via.placeholder.com/400x400/764ba2/ffffff?text=Leather+Jacket',
    ],
    inventory: 15,
    isActive: true,
    rating: 4.7,
    reviewCount: 67,
    tags: ['leather', 'jacket', 'premium', 'fashion'],
    createdAt: '2024-01-22T14:20:00Z',
    updatedAt: '2024-01-28T10:30:00Z',
    specifications: {
      'Material': '100% Genuine Leather',
      'Sizes': 'S, M, L, XL',
      'Color': 'Black, Brown',
      'Care': 'Professional Clean Only',
    },
  },
  {
    id: 'product-5',
    vendorId: 'vendor-2',
    name: 'Casual Summer Dress',
    description: 'Comfortable and stylish summer dress for everyday wear',
    price: 30.100, // ~79.99 USD converted to BHD
    category: 'Fashion',
    images: [
      'https://via.placeholder.com/400x400/764ba2/ffffff?text=Summer+Dress',
    ],
    inventory: 25,
    isActive: true,
    rating: 4.5,
    reviewCount: 43,
    tags: ['dress', 'summer', 'casual', 'comfortable'],
    createdAt: '2024-01-24T11:45:00Z',
    updatedAt: '2024-01-30T15:20:00Z',
    specifications: {
      'Material': '95% Cotton, 5% Elastane',
      'Sizes': 'XS, S, M, L, XL',
      'Colors': 'Blue, Pink, White, Yellow',
      'Length': 'Knee-length',
    },
  },

  // Sports Central Products
  {
    id: 'product-6',
    vendorId: 'vendor-4',
    name: 'Professional Tennis Racket',
    description: 'High-performance tennis racket for competitive players',
    price: 60.200, // ~159.99 USD converted to BHD
    category: 'Sports & Outdoors',
    images: [
      'https://via.placeholder.com/400x400/FF9800/ffffff?text=Tennis+Racket',
    ],
    inventory: 20,
    isActive: true,
    rating: 4.8,
    reviewCount: 92,
    tags: ['tennis', 'racket', 'professional', 'sports'],
    createdAt: '2024-01-27T13:15:00Z',
    updatedAt: '2024-02-02T09:40:00Z',
    specifications: {
      'Weight': '300g',
      'Head Size': '100 sq in',
      'String Pattern': '16x19',
      'Grip Size': '4 1/4"',
    },
  },
  {
    id: 'product-7',
    vendorId: 'vendor-4',
    name: 'Hiking Backpack 40L',
    description: 'Durable hiking backpack with multiple compartments',
    price: 48.900, // ~129.99 USD converted to BHD
    originalPrice: 60.200, // ~159.99 USD converted to BHD
    category: 'Sports & Outdoors',
    images: [
      'https://via.placeholder.com/400x400/FF9800/ffffff?text=Hiking+Backpack',
    ],
    inventory: 12,
    isActive: true,
    rating: 4.6,
    reviewCount: 78,
    tags: ['hiking', 'backpack', 'outdoor', 'travel'],
    createdAt: '2024-01-29T16:00:00Z',
    updatedAt: '2024-02-01T12:25:00Z',
    specifications: {
      'Capacity': '40L',
      'Material': 'Ripstop Nylon',
      'Weight': '1.2kg',
      'Features': 'Rain Cover, Hydration Compatible',
    },
  },
];

// Sample Orders
export const sampleOrders: Order[] = [
  {
    id: 'order-1',
    customerId: 'customer-1',
    customerName: 'Alice Cooper',
    customerEmail: '<EMAIL>',
    items: [
      {
        id: 'item-1',
        productId: 'product-1',
        vendorId: 'vendor-1',
        productName: 'Wireless Bluetooth Headphones',
        productImage: 'https://via.placeholder.com/400x400/667eea/ffffff?text=Headphones',
        quantity: 1,
        price: 75.200, // ~199.99 USD converted to BHD
        totalPrice: 75.200,
      },
    ],
    totalAmount: 75.200,
    status: ORDER_STATUS.DELIVERED,
    shippingAddress: {
      street: '123 Main St',
      city: 'Los Angeles',
      state: 'CA',
      zipCode: '90210',
      country: 'USA',
    },
    paymentMethod: 'Credit Card',
    createdAt: '2024-01-25T10:30:00Z',
    updatedAt: '2024-01-30T14:20:00Z',
    deliveredAt: '2024-01-30T14:20:00Z',
    trackingNumber: 'TRK123456789',
  },
  {
    id: 'order-2',
    customerId: 'customer-2',
    customerName: 'Bob Johnson',
    customerEmail: '<EMAIL>',
    items: [
      {
        id: 'item-2',
        productId: 'product-4',
        vendorId: 'vendor-2',
        productName: 'Designer Leather Jacket',
        productImage: 'https://via.placeholder.com/400x400/764ba2/ffffff?text=Leather+Jacket',
        quantity: 1,
        price: 112.800, // ~299.99 USD converted to BHD
        totalPrice: 112.800,
      },
      {
        id: 'item-3',
        productId: 'product-5',
        vendorId: 'vendor-2',
        productName: 'Casual Summer Dress',
        productImage: 'https://via.placeholder.com/400x400/764ba2/ffffff?text=Summer+Dress',
        quantity: 2,
        price: 30.100, // ~79.99 USD converted to BHD
        totalPrice: 60.200, // 30.100 * 2
      },
    ],
    totalAmount: 173.000, // 112.800 + 60.200
    status: ORDER_STATUS.PROCESSING,
    shippingAddress: {
      street: '456 Oak Ave',
      city: 'Chicago',
      state: 'IL',
      zipCode: '60601',
      country: 'USA',
    },
    paymentMethod: 'PayPal',
    createdAt: '2024-02-01T15:45:00Z',
    updatedAt: '2024-02-02T09:15:00Z',
    trackingNumber: 'TRK987654321',
  },
  {
    id: 'order-3',
    customerId: 'customer-3',
    customerName: 'Carol Davis',
    customerEmail: '<EMAIL>',
    items: [
      {
        id: 'item-4',
        productId: 'product-6',
        vendorId: 'vendor-4',
        productName: 'Professional Tennis Racket',
        productImage: 'https://via.placeholder.com/400x400/FF9800/ffffff?text=Tennis+Racket',
        quantity: 1,
        price: 60.200, // ~159.99 USD converted to BHD
        totalPrice: 60.200,
      },
    ],
    totalAmount: 60.200,
    status: ORDER_STATUS.PENDING,
    shippingAddress: {
      street: '789 Pine St',
      city: 'Miami',
      state: 'FL',
      zipCode: '33101',
      country: 'USA',
    },
    paymentMethod: 'Credit Card',
    createdAt: '2024-02-03T12:20:00Z',
    updatedAt: '2024-02-03T12:20:00Z',
  },
];
