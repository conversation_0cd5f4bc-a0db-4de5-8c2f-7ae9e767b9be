import React, { useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  ViewStyle,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useThemedStyles } from '../hooks';
import { GRADIENTS, SPACING } from '../constants/theme';
import type { ThemeColors } from '../contexts/ThemeContext';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'gradient' | 'pulse';
  color?: string;
  style?: ViewStyle;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  variant = 'default',
  color,
  style,
}) => {
  const styles = useThemedStyles(createStyles);
  const rotateValue = useRef(new Animated.Value(0)).current;
  const scaleValue = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (variant === 'gradient') {
      const rotateAnimation = Animated.loop(
        Animated.timing(rotateValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      );
      rotateAnimation.start();

      return () => rotateAnimation.stop();
    }

    if (variant === 'pulse') {
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(scaleValue, {
            toValue: 1.2,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(scaleValue, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();

      return () => pulseAnimation.stop();
    }
  }, [variant]);

  const getSize = () => {
    switch (size) {
      case 'small':
        return 20;
      case 'large':
        return 40;
      case 'medium':
      default:
        return 30;
    }
  };

  const containerStyle = [
    styles.container,
    style,
  ];

  if (variant === 'default') {
    return (
      <View style={containerStyle}>
        <ActivityIndicator
          size={size === 'small' ? 'small' : 'large'}
          color={color || styles.defaultColor.color}
        />
      </View>
    );
  }

  if (variant === 'gradient') {
    const rotate = rotateValue.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '360deg'],
    });

    return (
      <View style={containerStyle}>
        <Animated.View
          style={[
            styles.gradientSpinner,
            {
              width: getSize(),
              height: getSize(),
              transform: [{ rotate }],
            },
          ]}
        >
          <LinearGradient
            colors={GRADIENTS.primary}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.gradientFill}
          />
        </Animated.View>
      </View>
    );
  }

  if (variant === 'pulse') {
    return (
      <View style={containerStyle}>
        <Animated.View
          style={[
            styles.pulseSpinner,
            {
              width: getSize(),
              height: getSize(),
              transform: [{ scale: scaleValue }],
            },
          ]}
        />
      </View>
    );
  }

  return null;
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: SPACING.md,
    },
    defaultColor: {
      color: colors.primary,
    },
    gradientSpinner: {
      borderRadius: 50,
      overflow: 'hidden',
    },
    gradientFill: {
      flex: 1,
      borderRadius: 50,
    },
    pulseSpinner: {
      backgroundColor: colors.primary,
      borderRadius: 50,
      opacity: 0.7,
    },
  });
