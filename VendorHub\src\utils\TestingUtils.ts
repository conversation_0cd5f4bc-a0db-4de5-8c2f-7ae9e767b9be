import { ReactTestRenderer } from 'react-test-renderer';
import { RenderAPI } from '@testing-library/react-native';

// Mock Data Generators
export class MockDataGenerator {
  static generateUser(overrides: Partial<any> = {}): any {
    return {
      id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: 'Test User',
      email: '<EMAIL>',
      role: 'customer',
      createdAt: new Date().toISOString(),
      ...overrides,
    };
  }

  static generateVendor(overrides: Partial<any> = {}): any {
    return {
      id: `vendor_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      businessName: 'Test Business',
      ownerName: 'Test Owner',
      email: '<EMAIL>',
      phone: '+1234567890',
      address: {
        street: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        country: 'Test Country',
      },
      status: 'approved',
      createdAt: new Date().toISOString(),
      ...overrides,
    };
  }

  static generateProduct(overrides: Partial<any> = {}): any {
    return {
      id: `product_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: 'Test Product',
      description: 'A test product description',
      price: 99.99,
      originalPrice: 129.99,
      category: 'Electronics',
      vendorId: 'test_vendor_id',
      inventory: 50,
      rating: 4.5,
      reviewCount: 25,
      images: ['test_image_1.jpg', 'test_image_2.jpg'],
      featured: false,
      createdAt: new Date().toISOString(),
      ...overrides,
    };
  }

  static generateOrder(overrides: Partial<any> = {}): any {
    return {
      id: `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      customerId: 'test_customer_id',
      customerName: 'Test Customer',
      customerEmail: '<EMAIL>',
      items: [
        {
          id: 'item_1',
          productId: 'test_product_id',
          vendorId: 'test_vendor_id',
          productName: 'Test Product',
          quantity: 2,
          price: 99.99,
          totalPrice: 199.98,
        },
      ],
      totalAmount: 199.98,
      status: 'pending',
      shippingAddress: {
        street: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
        country: 'Test Country',
      },
      paymentMethod: 'credit_card',
      createdAt: new Date().toISOString(),
      ...overrides,
    };
  }

  static generateCartItem(overrides: Partial<any> = {}): any {
    return {
      productId: 'test_product_id',
      vendorId: 'test_vendor_id',
      quantity: 1,
      product: this.generateProduct(),
      ...overrides,
    };
  }

  static generateChatMessage(overrides: Partial<any> = {}): any {
    return {
      id: `message_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      chatId: 'test_chat_id',
      senderId: 'test_sender_id',
      senderName: 'Test Sender',
      receiverId: 'test_receiver_id',
      content: 'Test message content',
      type: 'text',
      timestamp: new Date().toISOString(),
      read: false,
      ...overrides,
    };
  }

  static generateAnalyticsEvent(overrides: Partial<any> = {}): any {
    return {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: 'page_view',
      category: 'user',
      timestamp: new Date().toISOString(),
      userId: 'test_user_id',
      sessionId: 'test_session_id',
      properties: { page: 'home' },
      ...overrides,
    };
  }
}

// Test Helpers
export class TestHelpers {
  static async waitFor(condition: () => boolean, timeout: number = 5000): Promise<void> {
    const startTime = Date.now();
    
    while (!condition() && Date.now() - startTime < timeout) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    if (!condition()) {
      throw new Error(`Condition not met within ${timeout}ms`);
    }
  }

  static async waitForElement(
    getByTestId: (testId: string) => any,
    testId: string,
    timeout: number = 5000
  ): Promise<any> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const element = getByTestId(testId);
        if (element) return element;
      } catch (error) {
        // Element not found, continue waiting
      }
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    throw new Error(`Element with testId "${testId}" not found within ${timeout}ms`);
  }

  static simulateAsyncOperation(duration: number = 1000): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, duration));
  }

  static createMockNavigation(overrides: Partial<any> = {}): any {
    return {
      navigate: jest.fn(),
      goBack: jest.fn(),
      push: jest.fn(),
      pop: jest.fn(),
      popToTop: jest.fn(),
      replace: jest.fn(),
      reset: jest.fn(),
      setParams: jest.fn(),
      dispatch: jest.fn(),
      isFocused: jest.fn(() => true),
      canGoBack: jest.fn(() => false),
      addListener: jest.fn(),
      removeListener: jest.fn(),
      ...overrides,
    };
  }

  static createMockRoute(params: any = {}): any {
    return {
      key: 'test-route-key',
      name: 'TestScreen',
      params,
    };
  }

  static createMockTheme(): any {
    return {
      colors: {
        primary: '#667eea',
        secondary: '#764ba2',
        background: '#FFFFFF',
        surface: '#F8F9FA',
        text: '#1A1A1A',
        textSecondary: '#666666',
        border: '#E0E0E0',
        error: '#F44336',
        success: '#4CAF50',
        warning: '#FF9800',
        info: '#2196F3',
      },
      isDark: false,
    };
  }
}

// Performance Testing
export class PerformanceTestUtils {
  static measureRenderTime<T>(renderFunction: () => T): { result: T; renderTime: number } {
    const startTime = performance.now();
    const result = renderFunction();
    const endTime = performance.now();
    
    return {
      result,
      renderTime: endTime - startTime,
    };
  }

  static async measureAsyncOperation<T>(
    operation: () => Promise<T>
  ): Promise<{ result: T; duration: number }> {
    const startTime = performance.now();
    const result = await operation();
    const endTime = performance.now();
    
    return {
      result,
      duration: endTime - startTime,
    };
  }

  static createMemorySnapshot(): any {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory;
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
        timestamp: Date.now(),
      };
    }
    return null;
  }

  static measureMemoryUsage<T>(operation: () => T): { result: T; memoryDelta: number | null } {
    const beforeSnapshot = this.createMemorySnapshot();
    const result = operation();
    const afterSnapshot = this.createMemorySnapshot();
    
    let memoryDelta = null;
    if (beforeSnapshot && afterSnapshot) {
      memoryDelta = afterSnapshot.usedJSHeapSize - beforeSnapshot.usedJSHeapSize;
    }
    
    return { result, memoryDelta };
  }
}

// API Testing
export class APITestUtils {
  static createMockResponse<T>(data: T, status: number = 200, delay: number = 0): Promise<T> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (status >= 200 && status < 300) {
          resolve(data);
        } else {
          reject(new Error(`HTTP ${status}`));
        }
      }, delay);
    });
  }

  static createMockErrorResponse(message: string, status: number = 500, delay: number = 0): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(message));
      }, delay);
    });
  }

  static mockAsyncStorage(): any {
    const storage: Record<string, string> = {};
    
    return {
      getItem: jest.fn((key: string) => Promise.resolve(storage[key] || null)),
      setItem: jest.fn((key: string, value: string) => {
        storage[key] = value;
        return Promise.resolve();
      }),
      removeItem: jest.fn((key: string) => {
        delete storage[key];
        return Promise.resolve();
      }),
      clear: jest.fn(() => {
        Object.keys(storage).forEach(key => delete storage[key]);
        return Promise.resolve();
      }),
      getAllKeys: jest.fn(() => Promise.resolve(Object.keys(storage))),
      multiGet: jest.fn((keys: string[]) => 
        Promise.resolve(keys.map(key => [key, storage[key] || null]))
      ),
      multiSet: jest.fn((keyValuePairs: [string, string][]) => {
        keyValuePairs.forEach(([key, value]) => {
          storage[key] = value;
        });
        return Promise.resolve();
      }),
      multiRemove: jest.fn((keys: string[]) => {
        keys.forEach(key => delete storage[key]);
        return Promise.resolve();
      }),
    };
  }
}

// Component Testing
export class ComponentTestUtils {
  static findByTestId(component: ReactTestRenderer, testId: string): any {
    return component.root.findByProps({ testID: testId });
  }

  static findAllByTestId(component: ReactTestRenderer, testId: string): any[] {
    return component.root.findAllByProps({ testID: testId });
  }

  static findByText(component: ReactTestRenderer, text: string): any {
    return component.root.findByProps({ children: text });
  }

  static findAllByText(component: ReactTestRenderer, text: string): any[] {
    return component.root.findAllByProps({ children: text });
  }

  static simulatePress(element: any): void {
    if (element.props.onPress) {
      element.props.onPress();
    }
  }

  static simulateTextInput(element: any, text: string): void {
    if (element.props.onChangeText) {
      element.props.onChangeText(text);
    }
  }

  static getProps(element: any): any {
    return element.props;
  }

  static hasProps(element: any, props: Record<string, any>): boolean {
    return Object.entries(props).every(([key, value]) => element.props[key] === value);
  }
}

// Accessibility Testing
export class AccessibilityTestUtils {
  static checkAccessibilityLabel(element: any, expectedLabel: string): boolean {
    return element.props.accessibilityLabel === expectedLabel;
  }

  static checkAccessibilityRole(element: any, expectedRole: string): boolean {
    return element.props.accessibilityRole === expectedRole;
  }

  static checkAccessibilityHint(element: any, expectedHint: string): boolean {
    return element.props.accessibilityHint === expectedHint;
  }

  static checkAccessibilityState(element: any, expectedState: Record<string, boolean>): boolean {
    const actualState = element.props.accessibilityState || {};
    return Object.entries(expectedState).every(([key, value]) => actualState[key] === value);
  }

  static findAccessibleElements(component: ReactTestRenderer): any[] {
    return component.root.findAll(node => 
      node.props.accessibilityLabel || 
      node.props.accessibilityRole || 
      node.props.accessibilityHint
    );
  }
}

// Test Data Management
export class TestDataManager {
  private static data: Record<string, any> = {};

  static setTestData(key: string, value: any): void {
    this.data[key] = value;
  }

  static getTestData<T>(key: string): T | undefined {
    return this.data[key];
  }

  static clearTestData(): void {
    this.data = {};
  }

  static createTestContext(initialData: Record<string, any> = {}): {
    setData: (key: string, value: any) => void;
    getData: <T>(key: string) => T | undefined;
    clearData: () => void;
  } {
    const contextData = { ...initialData };

    return {
      setData: (key: string, value: any) => {
        contextData[key] = value;
      },
      getData: <T>(key: string): T | undefined => {
        return contextData[key];
      },
      clearData: () => {
        Object.keys(contextData).forEach(key => delete contextData[key]);
      },
    };
  }
}

// Test Assertions
export class TestAssertions {
  static assertElementExists(element: any, message?: string): void {
    if (!element) {
      throw new Error(message || 'Element does not exist');
    }
  }

  static assertElementNotExists(element: any, message?: string): void {
    if (element) {
      throw new Error(message || 'Element should not exist');
    }
  }

  static assertTextContent(element: any, expectedText: string, message?: string): void {
    const actualText = element.props.children;
    if (actualText !== expectedText) {
      throw new Error(
        message || `Expected text "${expectedText}", but got "${actualText}"`
      );
    }
  }

  static assertPropsEqual(element: any, expectedProps: Record<string, any>, message?: string): void {
    const actualProps = element.props;
    const mismatches = Object.entries(expectedProps).filter(
      ([key, value]) => actualProps[key] !== value
    );

    if (mismatches.length > 0) {
      const mismatchDetails = mismatches
        .map(([key, value]) => `${key}: expected "${value}", got "${actualProps[key]}"`)
        .join(', ');
      throw new Error(
        message || `Props mismatch: ${mismatchDetails}`
      );
    }
  }

  static assertArrayLength<T>(array: T[], expectedLength: number, message?: string): void {
    if (array.length !== expectedLength) {
      throw new Error(
        message || `Expected array length ${expectedLength}, but got ${array.length}`
      );
    }
  }

  static assertObjectHasProperty(obj: any, property: string, message?: string): void {
    if (!(property in obj)) {
      throw new Error(
        message || `Object does not have property "${property}"`
      );
    }
  }

  static assertValueInRange(value: number, min: number, max: number, message?: string): void {
    if (value < min || value > max) {
      throw new Error(
        message || `Value ${value} is not in range [${min}, ${max}]`
      );
    }
  }
}

// Export all utilities
export {
  MockDataGenerator,
  TestHelpers,
  PerformanceTestUtils,
  APITestUtils,
  ComponentTestUtils,
  AccessibilityTestUtils,
  TestDataManager,
  TestAssertions,
};
