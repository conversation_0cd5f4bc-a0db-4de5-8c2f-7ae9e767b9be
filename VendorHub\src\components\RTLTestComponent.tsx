import React, { useState } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { useI18n } from '../hooks/useI18n';
import { RTLView, RTLText, RTLIcon } from './RTL';
import { Button } from './Button';
import { Card } from './Card';
import { RTLTestingComponent } from './RTLTestingComponent';

export const RTLTestComponent: React.FC = () => {
  const { t, currentLanguage, isRTL, changeLanguage } = useI18n();
  const [showAdvancedTesting, setShowAdvancedTesting] = useState(false);

  const testRTLLayout = () => {
    Alert.alert(
      t('common.success'),
      `Current Language: ${currentLanguage}\nIs RTL: ${isRTL}\nText Direction: ${isRTL ? 'Right to Left' : 'Left to Right'}`,
      [{ text: t('common.close') }]
    );
  };

  const switchToArabic = async () => {
    await changeLanguage('ar');
  };

  const switchToEnglish = async () => {
    await changeLanguage('en');
  };

  if (showAdvancedTesting) {
    return <RTLTestingComponent />;
  }

  return (
    <Card style={styles.container}>
      <RTLText style={styles.title} weight="bold">
        {t('common.loading')} - RTL Test
      </RTLText>
      
      <RTLView style={styles.row}>
        <RTLIcon name="language" size={24} color="#007AFF" />
        <RTLText style={styles.text}>
          {t('auth.welcome')} - Current: {currentLanguage}
        </RTLText>
      </RTLView>

      <RTLView style={styles.row}>
        <RTLIcon name="arrow-forward" size={24} color="#007AFF" />
        <RTLText style={styles.text}>
          {t('products.addToCart')}
        </RTLText>
      </RTLView>

      <RTLView style={styles.buttonContainer}>
        <Button
          title="Switch to Arabic"
          onPress={switchToArabic}
          variant="outline"
          size="small"
          style={styles.button}
        />
        
        <Button
          title="Switch to English"
          onPress={switchToEnglish}
          variant="outline"
          size="small"
          style={styles.button}
        />
        
        <Button
          title="Test RTL"
          onPress={testRTLLayout}
          variant="primary"
          size="small"
          style={styles.button}
        />
      </RTLView>

      <RTLView style={styles.buttonContainer}>
        <Button
          title="Advanced Testing"
          onPress={() => setShowAdvancedTesting(true)}
          variant="outline"
          size="small"
          style={styles.button}
        />
      </RTLView>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    padding: 16,
  },
  title: {
    fontSize: 18,
    marginBottom: 16,
    textAlign: 'center',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 8,
  },
  text: {
    fontSize: 16,
    marginLeft: 8,
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    flexWrap: 'wrap',
  },
  button: {
    marginHorizontal: 4,
    marginVertical: 4,
    minWidth: 80,
  },
});
