import React, { useEffect, useRef, useState } from 'react';
import { View, Text, StyleSheet, Animated, ViewStyle } from 'react-native';
import { useThemedStyles } from '../hooks';
import { SPACING, FONT_SIZES, FONT_WEIGHTS } from '../constants/theme';
import { formatNumber, formatCurrency } from '../utils';
import type { ThemeColors } from '../contexts/ThemeContext';

interface StatisticsCounterProps {
  value: number;
  label: string;
  format?: 'number' | 'currency' | 'percentage';
  duration?: number;
  delay?: number;
  style?: ViewStyle;
  valueColor?: string;
  labelColor?: string;
  size?: 'small' | 'medium' | 'large';
}

export const StatisticsCounter: React.FC<StatisticsCounterProps> = ({
  value,
  label,
  format = 'number',
  duration = 1500,
  delay = 0,
  style,
  valueColor,
  labelColor,
  size = 'medium',
}) => {
  const styles = useThemedStyles(createStyles);
  const animatedValue = useRef(new Animated.Value(0)).current;
  const [displayValue, setDisplayValue] = useState(0);

  useEffect(() => {
    const animation = Animated.timing(animatedValue, {
      toValue: value,
      duration,
      delay,
      useNativeDriver: false,
    });

    const listener = animatedValue.addListener(({ value: animValue }) => {
      setDisplayValue(animValue);
    });

    animation.start();

    return () => {
      animatedValue.removeListener(listener);
    };
  }, [value, duration, delay]);

  const formatValue = (val: number): string => {
    switch (format) {
      case 'currency':
        return formatCurrency(val); // Always use BHD
      case 'percentage':
        return `${val.toFixed(1)}%`;
      case 'number':
      default:
        return formatNumber(Math.round(val));
    }
  };

  const containerStyle = [
    styles.container,
    styles[size],
    style,
  ];

  const valueStyle = [
    styles.value,
    styles[`${size}Value`],
    valueColor && { color: valueColor },
  ];

  const labelStyle = [
    styles.label,
    styles[`${size}Label`],
    labelColor && { color: labelColor },
  ];

  return (
    <View style={containerStyle}>
      <Text style={valueStyle}>
        {formatValue(displayValue)}
      </Text>
      <Text style={labelStyle}>{label}</Text>
    </View>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      alignItems: 'center',
      justifyContent: 'center',
    },
    small: {
      padding: SPACING.sm,
    },
    medium: {
      padding: SPACING.md,
    },
    large: {
      padding: SPACING.lg,
    },
    value: {
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
      textAlign: 'center',
    },
    smallValue: {
      fontSize: FONT_SIZES.lg,
    },
    mediumValue: {
      fontSize: FONT_SIZES.xxl,
    },
    largeValue: {
      fontSize: FONT_SIZES.xxxl,
    },
    label: {
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textSecondary,
      textAlign: 'center',
      marginTop: SPACING.xs,
    },
    smallLabel: {
      fontSize: FONT_SIZES.xs,
    },
    mediumLabel: {
      fontSize: FONT_SIZES.sm,
    },
    largeLabel: {
      fontSize: FONT_SIZES.md,
    },
  });
