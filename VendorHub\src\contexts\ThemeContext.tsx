import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { useColorScheme } from 'react-native';
import { COLORS, STORAGE_KEYS } from '../constants';
import { storage } from '../utils/storage';

// Types
export type ThemeMode = 'light' | 'dark' | 'system';

export interface ThemeColors {
  primary: string;
  primaryDark: string;
  background: string;
  backgroundSecondary: string;
  surface: string;
  textPrimary: string;
  textSecondary: string;
  textDisabled: string;
  textOnPrimary: string;
  border: string;
  borderLight: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  glassBackground: string;
  glassBorder: string;
}

export interface ThemeState {
  mode: ThemeMode;
  isDark: boolean;
  colors: ThemeColors;
}

type ThemeAction =
  | { type: 'SET_THEME_MODE'; payload: ThemeMode }
  | { type: 'SET_IS_DARK'; payload: boolean };

interface ThemeContextType extends ThemeState {
  setThemeMode: (mode: ThemeMode) => Promise<void>;
  toggleTheme: () => Promise<void>;
}

// Light theme colors
const lightColors: ThemeColors = {
  primary: COLORS.primary,
  primaryDark: COLORS.primaryDark,
  background: COLORS.background,
  backgroundSecondary: COLORS.backgroundSecondary,
  surface: COLORS.surface,
  textPrimary: COLORS.textPrimary,
  textSecondary: COLORS.textSecondary,
  textDisabled: COLORS.textDisabled,
  textOnPrimary: COLORS.textOnPrimary,
  border: COLORS.border,
  borderLight: COLORS.borderLight,
  success: COLORS.success,
  warning: COLORS.warning,
  error: COLORS.error,
  info: COLORS.info,
  glassBackground: COLORS.glassBackground,
  glassBorder: COLORS.glassBorder,
};

// Dark theme colors
const darkColors: ThemeColors = {
  primary: COLORS.primary,
  primaryDark: COLORS.primaryDark,
  background: COLORS.dark.background,
  backgroundSecondary: COLORS.dark.backgroundSecondary,
  surface: COLORS.dark.surface,
  textPrimary: COLORS.dark.textPrimary,
  textSecondary: COLORS.dark.textSecondary,
  textDisabled: COLORS.textDisabled,
  textOnPrimary: COLORS.textOnPrimary,
  border: COLORS.dark.border,
  borderLight: COLORS.dark.border,
  success: COLORS.success,
  warning: COLORS.warning,
  error: COLORS.error,
  info: COLORS.info,
  glassBackground: 'rgba(0, 0, 0, 0.25)',
  glassBorder: 'rgba(255, 255, 255, 0.18)',
};

// Initial state
const initialState: ThemeState = {
  mode: 'system',
  isDark: false,
  colors: lightColors,
};

// Reducer
const themeReducer = (state: ThemeState, action: ThemeAction): ThemeState => {
  switch (action.type) {
    case 'SET_THEME_MODE':
      return { ...state, mode: action.payload };
    
    case 'SET_IS_DARK':
      return {
        ...state,
        isDark: action.payload,
        colors: action.payload ? darkColors : lightColors,
      };
    
    default:
      return state;
  }
};

// Create context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Provider component
export const ThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(themeReducer, initialState);
  const systemColorScheme = useColorScheme();

  // Load saved theme mode on app start
  useEffect(() => {
    loadSavedThemeMode();
  }, []);

  // Update theme when system color scheme changes or mode changes
  useEffect(() => {
    updateTheme();
  }, [state.mode, systemColorScheme]);

  const loadSavedThemeMode = async () => {
    try {
      const savedMode = await storage.getItem(STORAGE_KEYS.THEME_MODE);
      if (savedMode && ['light', 'dark', 'system'].includes(savedMode)) {
        dispatch({ type: 'SET_THEME_MODE', payload: savedMode as ThemeMode });
      }
    } catch (error) {
      console.error('Error loading theme mode:', error);
    }
  };

  const updateTheme = () => {
    let isDark = false;

    switch (state.mode) {
      case 'light':
        isDark = false;
        break;
      case 'dark':
        isDark = true;
        break;
      case 'system':
        isDark = systemColorScheme === 'dark';
        break;
    }

    dispatch({ type: 'SET_IS_DARK', payload: isDark });
  };

  const setThemeMode = async (mode: ThemeMode) => {
    try {
      await storage.setItem(STORAGE_KEYS.THEME_MODE, mode);
      dispatch({ type: 'SET_THEME_MODE', payload: mode });
    } catch (error) {
      console.error('Error saving theme mode:', error);
    }
  };

  const toggleTheme = async () => {
    const newMode = state.isDark ? 'light' : 'dark';
    await setThemeMode(newMode);
  };

  const value: ThemeContextType = {
    ...state,
    setThemeMode,
    toggleTheme,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};

// Hook to use theme context
export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

// Utility hook for creating themed styles
export const useThemedStyles = <T extends Record<string, any>>(
  createStyles: (colors: ThemeColors) => T
): T => {
  const { colors } = useTheme();
  return createStyles(colors);
};
