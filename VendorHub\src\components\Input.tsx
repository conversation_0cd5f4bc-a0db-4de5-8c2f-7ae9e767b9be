import React, { useState } from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextInputProps,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles, useI18n } from '../hooks';
import { RTLView, RTLText, RTLInput } from './RTL';
import {
  INPUT_HEIGHT,
  BORDER_RADIUS,
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
} from '../constants/theme';
import type { ThemeColors } from '../contexts/ThemeContext';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: keyof typeof Ionicons.glyphMap;
  rightIcon?: keyof typeof Ionicons.glyphMap;
  onRightIconPress?: () => void;
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'small' | 'medium' | 'large';
  required?: boolean;
  containerStyle?: ViewStyle;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  onRightIconPress,
  variant = 'outlined',
  size = 'medium',
  required = false,
  containerStyle,
  style,
  secureTextEntry,
  ...props
}) => {
  const styles = useThemedStyles(createStyles);
  const { isRTL } = useI18n();
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(!secureTextEntry);

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const inputContainerStyle = [
    styles.inputContainer,
    styles[variant],
    styles[size],
    isFocused && styles.focused,
    error && styles.error,
  ];

  const inputStyle = [
    styles.input,
    styles[`${size}Input`],
    leftIcon && styles.inputWithLeftIcon,
    (rightIcon || secureTextEntry) && styles.inputWithRightIcon,
    style,
  ];

  const renderRightIcon = () => {
    if (secureTextEntry) {
      return (
        <TouchableOpacity
          onPress={togglePasswordVisibility}
          style={styles.iconContainer}
        >
          <Ionicons
            name={isPasswordVisible ? 'eye-off' : 'eye'}
            size={20}
            color={styles.icon.color}
          />
        </TouchableOpacity>
      );
    }

    if (rightIcon) {
      return (
        <TouchableOpacity
          onPress={onRightIconPress}
          style={styles.iconContainer}
        >
          <Ionicons
            name={rightIcon}
            size={20}
            color={styles.icon.color}
          />
        </TouchableOpacity>
      );
    }

    return null;
  };

  // Determine icon positions based on RTL
  const leftIconToShow = isRTL ? rightIcon : leftIcon;
  const rightIconToShow = isRTL ? leftIcon : rightIcon;
  const leftIconPress = isRTL ? onRightIconPress : undefined;
  const rightIconPress = isRTL ? undefined : onRightIconPress;

  return (
    <RTLView style={[styles.container, containerStyle]}>
      {label && (
        <RTLText style={styles.label}>
          {label}
          {required && <RTLText style={styles.required}> *</RTLText>}
        </RTLText>
      )}

      <RTLView style={inputContainerStyle}>
        {leftIconToShow && (
          <RTLView style={styles.iconContainer}>
            <Ionicons
              name={leftIconToShow}
              size={20}
              color={styles.icon.color}
            />
          </RTLView>
        )}

        <RTLInput
          style={inputStyle}
          onFocus={handleFocus}
          onBlur={handleBlur}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          placeholderTextColor={styles.placeholder.color}
          {...props}
        />

        {(rightIconToShow || secureTextEntry) && (
          <TouchableOpacity
            style={styles.iconContainer}
            onPress={secureTextEntry ? togglePasswordVisibility : rightIconPress}
          >
            <Ionicons
              name={secureTextEntry ? (isPasswordVisible ? 'eye-off' : 'eye') : rightIconToShow!}
              size={20}
              color={styles.icon.color}
            />
          </TouchableOpacity>
        )}
      </RTLView>

      {error && <RTLText style={styles.errorText}>{error}</RTLText>}
      {helperText && !error && <RTLText style={styles.helperText}>{helperText}</RTLText>}
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      marginBottom: SPACING.md,
    },
    label: {
      fontSize: FONT_SIZES.sm,
      fontWeight: FONT_WEIGHTS.medium,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    required: {
      color: colors.error,
    },
    inputContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: colors.surface,
    },
    default: {
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
      backgroundColor: 'transparent',
      borderRadius: 0,
    },
    outlined: {
      borderWidth: 1,
      borderColor: colors.border,
    },
    filled: {
      backgroundColor: colors.backgroundSecondary,
      borderWidth: 0,
    },
    small: {
      height: INPUT_HEIGHT * 0.8,
    },
    medium: {
      height: INPUT_HEIGHT,
    },
    large: {
      height: INPUT_HEIGHT * 1.2,
    },
    focused: {
      borderColor: colors.primary,
      borderWidth: 2,
    },
    error: {
      borderColor: colors.error,
      borderWidth: 1,
    },
    input: {
      flex: 1,
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
      paddingHorizontal: SPACING.md,
    },
    smallInput: {
      fontSize: FONT_SIZES.sm,
    },
    mediumInput: {
      fontSize: FONT_SIZES.md,
    },
    largeInput: {
      fontSize: FONT_SIZES.lg,
    },
    inputWithLeftIcon: {
      paddingLeft: SPACING.xs,
    },
    inputWithRightIcon: {
      paddingRight: SPACING.xs,
    },
    iconContainer: {
      padding: SPACING.sm,
      justifyContent: 'center',
      alignItems: 'center',
    },
    icon: {
      color: colors.textSecondary,
    },
    placeholder: {
      color: colors.textSecondary,
    },
    errorText: {
      fontSize: FONT_SIZES.xs,
      color: colors.error,
      marginTop: SPACING.xs,
    },
    helperText: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginTop: SPACING.xs,
    },
  });
