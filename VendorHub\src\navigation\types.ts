import type { NavigatorScreenParams } from '@react-navigation/native';
import type { StackScreenProps } from '@react-navigation/stack';
import type { BottomTabScreenProps } from '@react-navigation/bottom-tabs';

// Root navigation types
export type RootStackParamList = {
  Auth: NavigatorScreenParams<AuthStackParamList>;
  Admin: NavigatorScreenParams<AdminStackParamList>;
  Vendor: NavigatorScreenParams<VendorStackParamList>;
  Customer: NavigatorScreenParams<CustomerStackParamList>;
};

// Auth navigation types
export type AuthStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Register: undefined;
};

// Admin navigation types
export type AdminTabParamList = {
  Dashboard: undefined;
  Vendors: undefined;
  Products: undefined;
  Orders: undefined;
};

export type AdminStackParamList = {
  AdminTabs: NavigatorScreenParams<AdminTabParamList>;
};

// Vendor navigation types
export type VendorTabParamList = {
  Overview: undefined;
  Products: undefined;
  Orders: undefined;
  Shop: undefined;
};

export type VendorStackParamList = {
  VendorPending: undefined;
  VendorTabs: NavigatorScreenParams<VendorTabParamList>;
  AddProduct: undefined;
  EditProduct: { productId: string };
  OrderDetails: { orderId: string };
  ShopSettings: undefined;
};

// Customer navigation types
export type CustomerTabParamList = {
  Home: undefined;
  Shops: undefined;
  Cart: undefined;
  Messages: undefined;
  Profile: undefined;
};

export type CustomerStackParamList = {
  CustomerTabs: NavigatorScreenParams<CustomerTabParamList>;
  ProductDetails: { productId: string };
  VendorShop: { vendorId: string };
  Checkout: undefined;
  OrderHistory: undefined;
  OrderDetails: { orderId: string };
  Search: undefined;
  Chat: { chatId: string; vendorId?: string; vendorName?: string };
};

// Screen props types for type-safe navigation
export type AuthScreenProps<T extends keyof AuthStackParamList> = StackScreenProps<
  AuthStackParamList,
  T
>;

export type AdminTabScreenProps<T extends keyof AdminTabParamList> = BottomTabScreenProps<
  AdminTabParamList,
  T
>;

export type AdminStackScreenProps<T extends keyof AdminStackParamList> = StackScreenProps<
  AdminStackParamList,
  T
>;

export type VendorTabScreenProps<T extends keyof VendorTabParamList> = BottomTabScreenProps<
  VendorTabParamList,
  T
>;

export type VendorStackScreenProps<T extends keyof VendorStackParamList> = StackScreenProps<
  VendorStackParamList,
  T
>;

export type CustomerTabScreenProps<T extends keyof CustomerTabParamList> = BottomTabScreenProps<
  CustomerTabParamList,
  T
>;

export type CustomerStackScreenProps<T extends keyof CustomerStackParamList> = StackScreenProps<
  CustomerStackParamList,
  T
>;

// Declare global types for React Navigation
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}
