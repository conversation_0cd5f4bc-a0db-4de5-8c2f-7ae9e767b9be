export { AppNavigator } from './AppNavigator';
export { AuthNavigator } from './AuthNavigator';
export { AdminNavigator } from './AdminNavigator';
export { VendorNavigator } from './VendorNavigator';
export { CustomerNavigator } from './CustomerNavigator';

// Export navigation types
export type {
  RootStackParamList,
  AuthStackParamList,
  AdminTabParamList,
  AdminStackParamList,
  VendorTabParamList,
  VendorStackParamList,
  CustomerTabParamList,
  CustomerStackParamList,
  AuthScreenProps,
  AdminTabScreenProps,
  AdminStackScreenProps,
  VendorTabScreenProps,
  VendorStackScreenProps,
  CustomerTabScreenProps,
  CustomerStackScreenProps,
} from './types';

// Export navigation utilities
export {
  useCustomerStackNavigation,
  useCustomerTabNavigation,
  useVendorStackNavigation,
  useVendorTabNavigation,
  useAdminStackNavigation,
  useAdminTabNavigation,
  useAuthStackNavigation,
  useCustomerStackRoute,
  useCustomerTabRoute,
  useVendorStackRoute,
  useVendorTabRoute,
  useAuthStackRoute,
} from './navigationUtils';

export type {
  CustomerStackNavigationProp,
  CustomerTabNavigationProp,
  CustomerStackRouteProp,
  CustomerTabRouteProp,
  VendorStackNavigationProp,
  VendorTabNavigationProp,
  VendorStackRouteProp,
  VendorTabRouteProp,
  AdminStackNavigationProp,
  AdminTabNavigationProp,
  AuthStackNavigationProp,
  AuthStackRouteProp,
} from './navigationUtils';
