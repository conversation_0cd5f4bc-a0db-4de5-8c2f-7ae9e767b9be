import { EventEmitter } from '../utils/EventEmitter';
import { storage } from '../utils/storage';

export interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  unit: 'ms' | 'mb' | 'fps' | 'count' | 'percentage';
  timestamp: string;
  category: 'navigation' | 'rendering' | 'memory' | 'network' | 'user_interaction';
  metadata?: Record<string, any>;
}

export interface PerformanceReport {
  id: string;
  timestamp: string;
  duration: number;
  metrics: PerformanceMetric[];
  deviceInfo: {
    platform: string;
    version: string;
    model?: string;
    memory?: number;
  };
  appInfo: {
    version: string;
    buildNumber: string;
    environment: 'development' | 'staging' | 'production';
  };
}

export interface PerformanceThresholds {
  navigationTime: number; // ms
  renderTime: number; // ms
  memoryUsage: number; // mb
  networkTimeout: number; // ms
  frameRate: number; // fps
}

export interface CrashReport {
  id: string;
  timestamp: string;
  error: {
    message: string;
    stack?: string;
    componentStack?: string;
  };
  userActions: string[];
  deviceInfo: any;
  appState: any;
}

class PerformanceService extends EventEmitter {
  private static instance: PerformanceService;
  private metrics: PerformanceMetric[] = [];
  private reports: PerformanceReport[] = [];
  private crashes: CrashReport[] = [];
  private userActions: string[] = [];
  private thresholds: PerformanceThresholds;
  private isMonitoring: boolean = false;
  private performanceObserver: any = null;

  private constructor() {
    super();
    this.thresholds = {
      navigationTime: 1000, // 1 second
      renderTime: 16, // 60fps = 16ms per frame
      memoryUsage: 100, // 100MB
      networkTimeout: 5000, // 5 seconds
      frameRate: 55, // Minimum acceptable FPS
    };
    this.loadStoredData();
  }

  public static getInstance(): PerformanceService {
    if (!PerformanceService.instance) {
      PerformanceService.instance = new PerformanceService();
    }
    return PerformanceService.instance;
  }

  // Monitoring Control
  public startMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.setupPerformanceObserver();
    this.startMemoryMonitoring();
    this.emit('monitoringStarted');
  }

  public stopMonitoring(): void {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    this.cleanupPerformanceObserver();
    this.emit('monitoringStopped');
  }

  // Metric Collection
  public recordMetric(
    name: string,
    value: number,
    unit: PerformanceMetric['unit'],
    category: PerformanceMetric['category'],
    metadata?: Record<string, any>
  ): void {
    const metric: PerformanceMetric = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      name,
      value,
      unit,
      timestamp: new Date().toISOString(),
      category,
      metadata,
    };

    this.metrics.push(metric);
    this.checkThresholds(metric);
    this.emit('metricRecorded', metric);

    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics.splice(0, this.metrics.length - 1000);
    }
  }

  public startTimer(name: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.recordMetric(name, duration, 'ms', 'user_interaction');
    };
  }

  // Navigation Performance
  public recordNavigationStart(screenName: string): void {
    this.recordMetric(`navigation_start_${screenName}`, performance.now(), 'ms', 'navigation');
  }

  public recordNavigationEnd(screenName: string): void {
    this.recordMetric(`navigation_end_${screenName}`, performance.now(), 'ms', 'navigation');
  }

  public measureNavigationTime(screenName: string, startTime: number): void {
    const endTime = performance.now();
    const duration = endTime - startTime;
    this.recordMetric(`navigation_time_${screenName}`, duration, 'ms', 'navigation');
  }

  // Rendering Performance
  public recordRenderTime(componentName: string, renderTime: number): void {
    this.recordMetric(`render_time_${componentName}`, renderTime, 'ms', 'rendering');
  }

  public recordFrameRate(fps: number): void {
    this.recordMetric('frame_rate', fps, 'fps', 'rendering');
  }

  // Memory Monitoring
  public recordMemoryUsage(): void {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      const memory = (performance as any).memory;
      this.recordMetric('memory_used', memory.usedJSHeapSize / 1024 / 1024, 'mb', 'memory');
      this.recordMetric('memory_total', memory.totalJSHeapSize / 1024 / 1024, 'mb', 'memory');
      this.recordMetric('memory_limit', memory.jsHeapSizeLimit / 1024 / 1024, 'mb', 'memory');
    }
  }

  // Network Performance
  public recordNetworkRequest(
    url: string,
    method: string,
    duration: number,
    status: number,
    size?: number
  ): void {
    this.recordMetric('network_request_time', duration, 'ms', 'network', {
      url,
      method,
      status,
      size,
    });
  }

  // User Actions
  public recordUserAction(action: string): void {
    const timestamp = new Date().toISOString();
    this.userActions.push(`${timestamp}: ${action}`);
    
    // Keep only last 50 actions
    if (this.userActions.length > 50) {
      this.userActions.splice(0, this.userActions.length - 50);
    }
  }

  // Error Tracking
  public recordCrash(error: Error, componentStack?: string): void {
    const crash: CrashReport = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString(),
      error: {
        message: error.message,
        stack: error.stack,
        componentStack,
      },
      userActions: [...this.userActions],
      deviceInfo: this.getDeviceInfo(),
      appState: this.getAppState(),
    };

    this.crashes.push(crash);
    this.emit('crashReported', crash);

    // Keep only last 10 crashes
    if (this.crashes.length > 10) {
      this.crashes.splice(0, this.crashes.length - 10);
    }

    this.saveData();
  }

  // Reporting
  public generateReport(): PerformanceReport {
    const report: PerformanceReport = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString(),
      duration: this.getSessionDuration(),
      metrics: [...this.metrics],
      deviceInfo: this.getDeviceInfo(),
      appInfo: this.getAppInfo(),
    };

    this.reports.push(report);
    
    // Keep only last 5 reports
    if (this.reports.length > 5) {
      this.reports.splice(0, this.reports.length - 5);
    }

    this.saveData();
    return report;
  }

  public getMetrics(category?: PerformanceMetric['category']): PerformanceMetric[] {
    if (category) {
      return this.metrics.filter(metric => metric.category === category);
    }
    return [...this.metrics];
  }

  public getCrashes(): CrashReport[] {
    return [...this.crashes];
  }

  public getReports(): PerformanceReport[] {
    return [...this.reports];
  }

  // Analytics
  public getAverageMetric(name: string): number {
    const relevantMetrics = this.metrics.filter(metric => metric.name === name);
    if (relevantMetrics.length === 0) return 0;
    
    const sum = relevantMetrics.reduce((acc, metric) => acc + metric.value, 0);
    return sum / relevantMetrics.length;
  }

  public getPerformanceScore(): number {
    const navigationScore = this.calculateNavigationScore();
    const renderingScore = this.calculateRenderingScore();
    const memoryScore = this.calculateMemoryScore();
    const networkScore = this.calculateNetworkScore();

    return Math.round((navigationScore + renderingScore + memoryScore + networkScore) / 4);
  }

  // Private Methods
  private setupPerformanceObserver(): void {
    if (typeof PerformanceObserver !== 'undefined') {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.recordMetric(
            entry.name,
            entry.duration || entry.startTime,
            'ms',
            'rendering',
            { entryType: entry.entryType }
          );
        });
      });

      try {
        this.performanceObserver.observe({ entryTypes: ['measure', 'navigation'] });
      } catch (error) {
        console.warn('Performance Observer not fully supported:', error);
      }
    }
  }

  private cleanupPerformanceObserver(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
      this.performanceObserver = null;
    }
  }

  private startMemoryMonitoring(): void {
    const interval = setInterval(() => {
      if (!this.isMonitoring) {
        clearInterval(interval);
        return;
      }
      this.recordMemoryUsage();
    }, 5000); // Every 5 seconds
  }

  private checkThresholds(metric: PerformanceMetric): void {
    let threshold: number | undefined;
    
    switch (metric.name) {
      case 'navigation_time':
        threshold = this.thresholds.navigationTime;
        break;
      case 'render_time':
        threshold = this.thresholds.renderTime;
        break;
      case 'memory_used':
        threshold = this.thresholds.memoryUsage;
        break;
      case 'network_request_time':
        threshold = this.thresholds.networkTimeout;
        break;
      case 'frame_rate':
        if (metric.value < this.thresholds.frameRate) {
          this.emit('performanceIssue', { metric, threshold: this.thresholds.frameRate });
        }
        return;
    }

    if (threshold && metric.value > threshold) {
      this.emit('performanceIssue', { metric, threshold });
    }
  }

  private calculateNavigationScore(): number {
    const avgNavigationTime = this.getAverageMetric('navigation_time');
    if (avgNavigationTime === 0) return 100;
    
    const score = Math.max(0, 100 - (avgNavigationTime / this.thresholds.navigationTime) * 50);
    return Math.round(score);
  }

  private calculateRenderingScore(): number {
    const avgFrameRate = this.getAverageMetric('frame_rate');
    if (avgFrameRate === 0) return 100;
    
    const score = Math.min(100, (avgFrameRate / 60) * 100);
    return Math.round(score);
  }

  private calculateMemoryScore(): number {
    const avgMemoryUsage = this.getAverageMetric('memory_used');
    if (avgMemoryUsage === 0) return 100;
    
    const score = Math.max(0, 100 - (avgMemoryUsage / this.thresholds.memoryUsage) * 50);
    return Math.round(score);
  }

  private calculateNetworkScore(): number {
    const avgNetworkTime = this.getAverageMetric('network_request_time');
    if (avgNetworkTime === 0) return 100;
    
    const score = Math.max(0, 100 - (avgNetworkTime / this.thresholds.networkTimeout) * 50);
    return Math.round(score);
  }

  private getSessionDuration(): number {
    const firstMetric = this.metrics[0];
    const lastMetric = this.metrics[this.metrics.length - 1];
    
    if (!firstMetric || !lastMetric) return 0;
    
    const start = new Date(firstMetric.timestamp).getTime();
    const end = new Date(lastMetric.timestamp).getTime();
    
    return end - start;
  }

  private getDeviceInfo(): any {
    return {
      platform: 'react-native',
      version: '0.72.0', // This would be dynamic in a real app
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
    };
  }

  private getAppInfo(): any {
    return {
      version: '1.0.0', // This would come from app config
      buildNumber: '1', // This would come from app config
      environment: __DEV__ ? 'development' : 'production',
    };
  }

  private getAppState(): any {
    return {
      timestamp: new Date().toISOString(),
      metricsCount: this.metrics.length,
      isMonitoring: this.isMonitoring,
    };
  }

  // Storage
  private async loadStoredData(): Promise<void> {
    try {
      const [metricsData, reportsData, crashesData] = await Promise.all([
        storage.getItem('performanceMetrics'),
        storage.getItem('performanceReports'),
        storage.getItem('crashReports'),
      ]);

      if (metricsData) {
        this.metrics = JSON.parse(metricsData);
      }

      if (reportsData) {
        this.reports = JSON.parse(reportsData);
      }

      if (crashesData) {
        this.crashes = JSON.parse(crashesData);
      }
    } catch (error) {
      console.error('Error loading performance data:', error);
    }
  }

  private async saveData(): Promise<void> {
    try {
      await Promise.all([
        storage.setItem('performanceMetrics', JSON.stringify(this.metrics)),
        storage.setItem('performanceReports', JSON.stringify(this.reports)),
        storage.setItem('crashReports', JSON.stringify(this.crashes)),
      ]);
    } catch (error) {
      console.error('Error saving performance data:', error);
    }
  }

  // Configuration
  public updateThresholds(newThresholds: Partial<PerformanceThresholds>): void {
    this.thresholds = { ...this.thresholds, ...newThresholds };
  }

  public getThresholds(): PerformanceThresholds {
    return { ...this.thresholds };
  }
}

export default PerformanceService.getInstance();
