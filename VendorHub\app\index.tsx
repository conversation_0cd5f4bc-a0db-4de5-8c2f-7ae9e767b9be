import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useAuth, useTheme } from '../src/hooks';
import { AppNavigator, AuthNavigator } from '../src/navigation';
import { Button, LoadingSpinner } from '../src/components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  GRADIENTS,
  BORDER_RADIUS
} from '../src/constants/theme';
import { APP_NAME } from '../src/constants';
import type { ThemeColors } from '../src/contexts/ThemeContext';

// AppContent component that conditionally renders based on authentication state
const AppContent: React.FC = () => {
  const { isAuthenticated, isLoading, loginAsVendor } = useAuth();
  const { colors } = useTheme();
  const [showAuthFlow, setShowAuthFlow] = useState(false);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <LoadingSpinner size="large" variant="gradient" />
      </View>
    );
  }

  // When unauthenticated: show login interface with "Login as Vendor" button or auth flow
  if (!isAuthenticated) {
    if (showAuthFlow) {
      return <AuthNavigator />;
    }

    return (
      <LinearGradient colors={GRADIENTS.primary} style={styles.container}>
        <View style={styles.loginInterface}>
          <View style={styles.header}>
            <Text style={styles.logoText}>{APP_NAME}</Text>
            <Text style={styles.tagline}>Multi-Vendor Mobile Marketplace</Text>
            <Text style={styles.description}>
              Connect vendors and customers in one seamless platform
            </Text>
          </View>

          <View style={styles.loginActions}>
            <Button
              title="Login as Vendor"
              onPress={loginAsVendor}
              variant="primary"
              size="large"
              style={styles.vendorLoginButton}
              leftIcon={<Ionicons name="storefront-outline" size={20} color="#FFFFFF" />}
              loading={isLoading}
              disabled={isLoading}
            />

            <Text style={styles.orText}>or</Text>

            <Button
              title="Continue to App"
              onPress={() => setShowAuthFlow(true)}
              variant="outline"
              size="large"
              style={styles.continueButton}
              textStyle={styles.continueButtonText}
            />
          </View>
        </View>
      </LinearGradient>
    );
  }

  // When authenticated: render AppNavigator component
  return <AppNavigator />;
};

export default function App() {
  return <AppContent />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loginInterface: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xxl * 2,
  },
  logoText: {
    fontSize: FONT_SIZES.xxxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: SPACING.md,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  tagline: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.medium,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: SPACING.sm,
    opacity: 0.9,
  },
  description: {
    fontSize: FONT_SIZES.md,
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.8,
    lineHeight: 22,
  },
  loginActions: {
    width: '100%',
    alignItems: 'center',
  },
  vendorLoginButton: {
    width: '100%',
    marginBottom: SPACING.lg,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderWidth: 1,
  },
  orText: {
    fontSize: FONT_SIZES.md,
    color: '#FFFFFF',
    opacity: 0.7,
    marginBottom: SPACING.lg,
    fontWeight: FONT_WEIGHTS.medium,
  },
  continueButton: {
    width: '100%',
    backgroundColor: 'transparent',
    borderColor: 'rgba(255, 255, 255, 0.5)',
    borderWidth: 1,
  },
  continueButtonText: {
    color: '#FFFFFF',
  },
});
