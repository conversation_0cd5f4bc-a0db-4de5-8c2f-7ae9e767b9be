import React, { useMemo, useCallback, useRef } from 'react';
import {
  FlatList,
  FlatListProps,
  ViewToken,
  Dimensions,
  Image,
} from 'react-native';
import { useThemedStyles } from '../../hooks';
import { SPACING } from '../../constants/theme';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { height: screenHeight } = Dimensions.get('window');

export interface OptimizedFlatListProps<T> extends Omit<FlatListProps<T>, 'getItemLayout'> {
  data: T[];
  renderItem: ({ item, index }: { item: T; index: number }) => React.ReactElement;
  keyExtractor: (item: T, index: number) => string;
  itemHeight?: number;
  estimatedItemSize?: number;
  enableVirtualization?: boolean;
  enableMemoryOptimization?: boolean;
  enableScrollOptimization?: boolean;
  onViewableItemsChanged?: (info: { viewableItems: ViewToken[]; changed: ViewToken[] }) => void;
  viewabilityConfig?: {
    itemVisiblePercentThreshold?: number;
    minimumViewTime?: number;
    waitForInteraction?: boolean;
  };
}

export const OptimizedFlatList = <T,>({
  data,
  renderItem,
  keyExtractor,
  itemHeight,
  estimatedItemSize = 100,
  enableVirtualization = true,
  enableMemoryOptimization = true,
  enableScrollOptimization = true,
  onViewableItemsChanged,
  viewabilityConfig,
  ...props
}: OptimizedFlatListProps<T>) => {
  const styles = useThemedStyles(createStyles);
  const flatListRef = useRef<FlatList<T>>(null);

  // Optimized getItemLayout for known item heights
  const getItemLayout = useMemo(() => {
    if (!itemHeight) return undefined;
    
    return (data: T[] | null | undefined, index: number) => ({
      length: itemHeight,
      offset: itemHeight * index,
      index,
    });
  }, [itemHeight]);

  // Memoized render item to prevent unnecessary re-renders
  const memoizedRenderItem = useCallback(
    ({ item, index }: { item: T; index: number }) => {
      return renderItem({ item, index });
    },
    [renderItem]
  );

  // Optimized viewability config
  const optimizedViewabilityConfig = useMemo(() => ({
    itemVisiblePercentThreshold: 50,
    minimumViewTime: 100,
    waitForInteraction: true,
    ...viewabilityConfig,
  }), [viewabilityConfig]);

  // Calculate optimal window size based on screen height and item size
  const windowSize = useMemo(() => {
    const itemsPerScreen = Math.ceil(screenHeight / estimatedItemSize);
    return Math.max(5, itemsPerScreen * 2); // At least 5, or 2 screens worth
  }, [estimatedItemSize]);

  // Calculate initial number of items to render
  const initialNumToRender = useMemo(() => {
    const itemsPerScreen = Math.ceil(screenHeight / estimatedItemSize);
    return Math.max(10, itemsPerScreen + 5); // At least 10, or 1 screen + buffer
  }, [estimatedItemSize]);

  // Calculate max render per batch
  const maxToRenderPerBatch = useMemo(() => {
    return Math.max(5, Math.ceil(initialNumToRender / 2));
  }, [initialNumToRender]);

  return (
    <FlatList
      ref={flatListRef}
      data={data}
      renderItem={memoizedRenderItem}
      keyExtractor={keyExtractor}
      getItemLayout={getItemLayout}
      
      // Performance optimizations
      removeClippedSubviews={enableMemoryOptimization}
      disableVirtualization={!enableVirtualization}
      windowSize={windowSize}
      initialNumToRender={initialNumToRender}
      maxToRenderPerBatch={maxToRenderPerBatch}
      updateCellsBatchingPeriod={50}
      
      // Scroll optimizations
      scrollEventThrottle={enableScrollOptimization ? 16 : undefined}
      
      // Viewability optimizations
      onViewableItemsChanged={onViewableItemsChanged}
      viewabilityConfig={optimizedViewabilityConfig}
      
      // Memory optimizations
      legacyImplementation={false}
      
      {...props}
    />
  );
};

// Hook for managing large datasets with pagination
export const usePaginatedData = <T,>(
  allData: T[],
  pageSize: number = 20,
  initialPages: number = 1
) => {
  const [currentPage, setCurrentPage] = React.useState(initialPages);
  
  const paginatedData = useMemo(() => {
    return allData.slice(0, currentPage * pageSize);
  }, [allData, currentPage, pageSize]);
  
  const hasMore = useMemo(() => {
    return paginatedData.length < allData.length;
  }, [paginatedData.length, allData.length]);
  
  const loadMore = useCallback(() => {
    if (hasMore) {
      setCurrentPage(prev => prev + 1);
    }
  }, [hasMore]);
  
  const reset = useCallback(() => {
    setCurrentPage(initialPages);
  }, [initialPages]);
  
  return {
    data: paginatedData,
    hasMore,
    loadMore,
    reset,
    currentPage,
    totalPages: Math.ceil(allData.length / pageSize),
  };
};

// Hook for optimized search with debouncing and memoization
export const useOptimizedSearch = <T,>(
  data: T[],
  searchFields: (keyof T)[],
  debounceMs: number = 300
) => {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [debouncedQuery, setDebouncedQuery] = React.useState('');
  
  // Debounce search query
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, debounceMs);
    
    return () => clearTimeout(timer);
  }, [searchQuery, debounceMs]);
  
  // Memoized filtered data
  const filteredData = useMemo(() => {
    if (!debouncedQuery.trim()) return data;
    
    const query = debouncedQuery.toLowerCase();
    return data.filter(item => 
      searchFields.some(field => {
        const value = item[field];
        return typeof value === 'string' && value.toLowerCase().includes(query);
      })
    );
  }, [data, debouncedQuery, searchFields]);
  
  return {
    searchQuery,
    setSearchQuery,
    filteredData,
    isSearching: searchQuery !== debouncedQuery,
  };
};

// Component for lazy loading images in lists
export const LazyImage: React.FC<{
  source: { uri: string };
  style?: any;
  placeholder?: React.ReactNode;
  onLoad?: () => void;
  onError?: () => void;
}> = ({ source, style, placeholder, onLoad, onError }) => {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);
  
  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoad?.();
  }, [onLoad]);
  
  const handleError = useCallback(() => {
    setHasError(true);
    onError?.();
  }, [onError]);
  
  if (hasError) {
    return placeholder || null;
  }
  
  return (
    <>
      {!isLoaded && placeholder}
      <Image
        source={source}
        style={[style, !isLoaded && { opacity: 0 }]}
        onLoad={handleLoad}
        onError={handleError}
      />
    </>
  );
};

// Performance monitoring hook
export const useListPerformance = (listName: string) => {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());
  
  React.useEffect(() => {
    renderCount.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;
    lastRenderTime.current = now;
    
    if (__DEV__) {
      console.log(`[${listName}] Render #${renderCount.current}, Time since last: ${timeSinceLastRender}ms`);
    }
  });
  
  const logScrollPerformance = useCallback((event: any) => {
    if (__DEV__) {
      const { contentOffset, velocity } = event.nativeEvent;
      console.log(`[${listName}] Scroll - Offset: ${contentOffset.y}, Velocity: ${velocity?.y || 0}`);
    }
  }, [listName]);
  
  return { logScrollPerformance };
};

const createStyles = (colors: ThemeColors) => ({
  // Add any default styles here
});
