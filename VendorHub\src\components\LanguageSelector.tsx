import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles, useI18n } from '../hooks';
import { Card, Button, RTLView, RTLText, RTLIcon } from './';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../constants/theme';
import type { ThemeColors } from '../contexts/ThemeContext';
import type { LanguageConfig, SupportedLanguage } from '../services/I18nService';

interface LanguageSelectorProps {
  style?: any;
  showLabel?: boolean;
  compact?: boolean;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  style,
  showLabel = true,
  compact = false,
}) => {
  const styles = useThemedStyles(createStyles);
  const { 
    currentLanguage, 
    changeLanguage, 
    getSupportedLanguages, 
    getCurrentLanguageConfig,
    t 
  } = useI18n();
  
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isChanging, setIsChanging] = useState(false);

  const supportedLanguages = getSupportedLanguages();
  const currentConfig = getCurrentLanguageConfig();

  const handleLanguageSelect = async (language: SupportedLanguage) => {
    if (language === currentLanguage) {
      setIsModalVisible(false);
      return;
    }

    setIsChanging(true);
    try {
      await changeLanguage(language);
      setIsModalVisible(false);
    } catch (error) {
      console.error('Error changing language:', error);
    } finally {
      setIsChanging(false);
    }
  };

  const renderLanguageItem = ({ item }: { item: LanguageConfig }) => (
    <TouchableOpacity
      style={[
        styles.languageItem,
        item.code === currentLanguage && styles.selectedLanguageItem,
      ]}
      onPress={() => handleLanguageSelect(item.code)}
      disabled={isChanging}
    >
      <RTLView style={styles.languageInfo}>
        <RTLText style={styles.languageFlag}>{item.flag}</RTLText>
        <RTLView style={styles.languageText}>
          <RTLText style={styles.languageName}>{item.name}</RTLText>
          <RTLText style={styles.languageNativeName}>{item.nativeName}</RTLText>
        </RTLView>
      </RTLView>

      {item.code === currentLanguage && (
        <RTLIcon name="checkmark-circle" size={24} color="#4CAF50" />
      )}

      {item.rtl && (
        <RTLView style={styles.rtlBadge}>
          <RTLText style={styles.rtlBadgeText}>RTL</RTLText>
        </RTLView>
      )}
    </TouchableOpacity>
  );

  if (compact) {
    return (
      <>
        <TouchableOpacity
          style={[styles.compactSelector, style]}
          onPress={() => setIsModalVisible(true)}
        >
          <RTLText style={styles.compactFlag}>{currentConfig.flag}</RTLText>
          <RTLIcon name="chevron-down" size={16} color="#666" />
        </TouchableOpacity>

        <Modal
          visible={isModalVisible}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={() => setIsModalVisible(false)}
        >
          <SafeAreaView style={styles.modalContainer}>
            <RTLView style={styles.modalHeader}>
              <RTLText style={styles.modalTitle}>{t('common.selectLanguage')}</RTLText>
              <TouchableOpacity
                onPress={() => setIsModalVisible(false)}
                style={styles.closeButton}
              >
                <RTLIcon name="close" size={24} color="#666" />
              </TouchableOpacity>
            </RTLView>

            <FlatList
              data={supportedLanguages}
              renderItem={renderLanguageItem}
              keyExtractor={(item) => item.code}
              style={styles.languageList}
              showsVerticalScrollIndicator={false}
            />
          </SafeAreaView>
        </Modal>
      </>
    );
  }

  return (
    <>
      <TouchableOpacity
        style={[styles.selector, style]}
        onPress={() => setIsModalVisible(true)}
      >
        <View style={styles.selectorContent}>
          <Text style={styles.flag}>{currentConfig.flag}</Text>
          <View style={styles.textContainer}>
            {showLabel && (
              <Text style={styles.label}>{t('common.language')}</Text>
            )}
            <Text style={styles.selectedLanguage}>
              {currentConfig.nativeName}
            </Text>
          </View>
        </View>
        <Ionicons name="chevron-forward" size={20} color="#666" />
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setIsModalVisible(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{t('common.selectLanguage')}</Text>
            <TouchableOpacity
              onPress={() => setIsModalVisible(false)}
              style={styles.closeButton}
            >
              <Ionicons name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <FlatList
            data={supportedLanguages}
            renderItem={renderLanguageItem}
            keyExtractor={(item) => item.code}
            style={styles.languageList}
            showsVerticalScrollIndicator={false}
          />
        </SafeAreaView>
      </Modal>
    </>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.md,
    backgroundColor: colors.surface,
    borderRadius: BORDER_RADIUS.md,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectorContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  flag: {
    fontSize: 24,
    marginRight: SPACING.md,
  },
  textContainer: {
    flex: 1,
  },
  label: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  selectedLanguage: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
  },
  compactSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.sm,
    backgroundColor: colors.surface,
    borderRadius: BORDER_RADIUS.sm,
    borderWidth: 1,
    borderColor: colors.border,
    gap: SPACING.xs,
  },
  compactFlag: {
    fontSize: 18,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
  },
  closeButton: {
    padding: SPACING.sm,
  },
  languageList: {
    flex: 1,
    padding: SPACING.lg,
  },
  languageItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SPACING.md,
    backgroundColor: colors.surface,
    borderRadius: BORDER_RADIUS.md,
    marginBottom: SPACING.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  selectedLanguageItem: {
    borderColor: '#4CAF50',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
  },
  languageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  languageFlag: {
    fontSize: 24,
    marginRight: SPACING.md,
  },
  languageText: {
    flex: 1,
  },
  languageName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
  },
  languageNativeName: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
    marginTop: 2,
  },
  rtlBadge: {
    backgroundColor: '#FF9800',
    paddingHorizontal: SPACING.xs,
    paddingVertical: 2,
    borderRadius: BORDER_RADIUS.sm,
    marginLeft: SPACING.sm,
  },
  rtlBadgeText: {
    fontSize: FONT_SIZES.xs,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#FFFFFF',
  },
});
