import { useData } from '../contexts/DataContext';
import type { Product } from '../contexts/DataContext';

export const useCart = () => {
  const {
    cart,
    addToCart,
    updateCartItemQuantity,
    removeFromCart,
    clearCart,
    getCartTotal,
    getCartItemCount,
  } = useData();

  // Check if product is in cart
  const isInCart = (productId: string): boolean => {
    return cart.some(item => item.productId === productId);
  };

  // Get cart item by product ID
  const getCartItem = (productId: string) => {
    return cart.find(item => item.productId === productId);
  };

  // Get cart item quantity
  const getCartItemQuantity = (productId: string): number => {
    const item = getCartItem(productId);
    return item ? item.quantity : 0;
  };

  // Add or update cart item
  const addOrUpdateCartItem = (product: Product, quantity: number) => {
    const existingItem = getCartItem(product.id);
    
    if (existingItem) {
      updateCartItemQuantity(product.id, existingItem.quantity + quantity);
    } else {
      addToCart(product, quantity);
    }
  };

  // Increment cart item quantity
  const incrementCartItem = (productId: string) => {
    const item = getCartItem(productId);
    if (item) {
      updateCartItemQuantity(productId, item.quantity + 1);
    }
  };

  // Decrement cart item quantity
  const decrementCartItem = (productId: string) => {
    const item = getCartItem(productId);
    if (item) {
      if (item.quantity > 1) {
        updateCartItemQuantity(productId, item.quantity - 1);
      } else {
        removeFromCart(productId);
      }
    }
  };

  // Get cart summary
  const getCartSummary = () => {
    const itemCount = getCartItemCount();
    const subtotal = getCartTotal();
    const tax = subtotal * 0.08; // 8% tax rate
    const shipping = subtotal > 50 ? 0 : 9.99; // Free shipping over $50
    const total = subtotal + tax + shipping;

    return {
      itemCount,
      subtotal,
      tax,
      shipping,
      total,
    };
  };

  // Get cart items grouped by vendor
  const getCartItemsByVendor = () => {
    const groupedItems = cart.reduce((groups, item) => {
      const vendorId = item.vendorId;
      if (!groups[vendorId]) {
        groups[vendorId] = [];
      }
      groups[vendorId].push(item);
      return groups;
    }, {} as Record<string, typeof cart>);

    return groupedItems;
  };

  // Validate cart (check inventory, active products)
  const validateCart = () => {
    const issues: Array<{
      productId: string;
      productName: string;
      issue: 'out_of_stock' | 'insufficient_stock' | 'inactive_product';
      availableQuantity?: number;
    }> = [];

    cart.forEach(item => {
      const product = item.product;
      
      if (!product.isActive) {
        issues.push({
          productId: product.id,
          productName: product.name,
          issue: 'inactive_product',
        });
      } else if (product.inventory === 0) {
        issues.push({
          productId: product.id,
          productName: product.name,
          issue: 'out_of_stock',
        });
      } else if (product.inventory < item.quantity) {
        issues.push({
          productId: product.id,
          productName: product.name,
          issue: 'insufficient_stock',
          availableQuantity: product.inventory,
        });
      }
    });

    return {
      isValid: issues.length === 0,
      issues,
    };
  };

  // Get recommended products based on cart items
  const getRecommendedProducts = (allProducts: Product[], limit: number = 5): Product[] => {
    if (cart.length === 0) return [];

    // Get categories of items in cart
    const cartCategories = [...new Set(cart.map(item => item.product.category))];
    
    // Find products in same categories that are not in cart
    const recommendations = allProducts.filter(product => 
      product.isActive &&
      cartCategories.includes(product.category) &&
      !cart.some(item => item.productId === product.id)
    );

    // Sort by rating and return limited results
    return recommendations
      .sort((a, b) => b.rating - a.rating)
      .slice(0, limit);
  };

  // Calculate savings (if products have original prices)
  const getCartSavings = () => {
    return cart.reduce((savings, item) => {
      const product = item.product;
      if (product.originalPrice && product.originalPrice > product.price) {
        const itemSavings = (product.originalPrice - product.price) * item.quantity;
        return savings + itemSavings;
      }
      return savings;
    }, 0);
  };

  return {
    // Data
    cart,
    cartItems: cart, // Alias for compatibility

    // Computed values
    cartTotal: getCartTotal(),
    cartItemCount: getCartItemCount(),
    cartSummary: getCartSummary(),
    cartSavings: getCartSavings(),
    cartItemsByVendor: getCartItemsByVendor(),

    // Methods
    addToCart,
    addOrUpdateCartItem,
    updateQuantity: updateCartItemQuantity, // Alias for compatibility
    updateCartItemQuantity,
    incrementCartItem,
    decrementCartItem,
    removeFromCart,
    clearCart,
    isInCart,
    getCartItem,
    getCartItemQuantity,
    validateCart,
    getRecommendedProducts,
  };
};
