import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useThemedStyles, useAuth, useOrders, useVendors, useProducts } from '../../hooks';
import { Card, Button, EmptyState, StatusBadge } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
  ICON_SIZES,
} from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';

interface OrderDetailsScreenProps {
  navigation: any;
  route: {
    params: {
      orderId: string;
    };
  };
}

export const OrderDetailsScreen: React.FC<OrderDetailsScreenProps> = ({ navigation, route }) => {
  const { orderId } = route.params;
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const { getOrderById } = useOrders();
  const { getVendorById } = useVendors();
  const { getProductById } = useProducts();
  
  const [refreshing, setRefreshing] = useState(false);

  const order = getOrderById(orderId);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#FF9800';
      case 'confirmed': return '#2196F3';
      case 'shipped': return '#9C27B0';
      case 'delivered': return '#4CAF50';
      case 'cancelled': return '#F44336';
      default: return '#757575';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'time-outline';
      case 'confirmed': return 'checkmark-circle-outline';
      case 'shipped': return 'car-outline';
      case 'delivered': return 'checkmark-done-outline';
      case 'cancelled': return 'close-circle-outline';
      default: return 'help-circle-outline';
    }
  };

  const getOrderTimeline = (status: string) => {
    const steps = [
      { key: 'pending', label: 'Order Placed', icon: 'receipt-outline' },
      { key: 'confirmed', label: 'Confirmed', icon: 'checkmark-circle-outline' },
      { key: 'shipped', label: 'Shipped', icon: 'car-outline' },
      { key: 'delivered', label: 'Delivered', icon: 'checkmark-done-outline' },
    ];

    const statusOrder = ['pending', 'confirmed', 'shipped', 'delivered'];
    const currentIndex = statusOrder.indexOf(status);

    return steps.map((step, index) => ({
      ...step,
      completed: index <= currentIndex,
      active: index === currentIndex,
    }));
  };

  const handleProductPress = (productId: string) => {
    navigation.navigate('ProductDetails', { productId });
  };

  const handleVendorPress = (vendorId: string) => {
    navigation.navigate('VendorShop', { vendorId });
  };

  const handleReorder = () => {
    Alert.alert(
      'Reorder Items',
      'Add all items from this order to your cart?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Add to Cart', onPress: () => {
          // Implementation would add items to cart
          Alert.alert('Success', 'Items added to cart!');
        }},
      ]
    );
  };

  if (!order) {
    return (
      <SafeAreaView style={styles.container}>
        <EmptyState
          icon="receipt-outline"
          title="Order Not Found"
          description="The order you're looking for doesn't exist or has been removed."
          actionButton={
            <Button
              title="Go Back"
              onPress={() => navigation.goBack()}
            />
          }
        />
      </SafeAreaView>
    );
  }

  const timeline = getOrderTimeline(order.status);

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Order Header */}
        <Card style={styles.headerCard} variant="elevated">
          <View style={styles.orderHeader}>
            <View style={styles.orderInfo}>
              <Text style={styles.orderId}>Order #{order.id.slice(-6)}</Text>
              <Text style={styles.orderDate}>
                Placed on {new Date(order.createdAt).toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </Text>
            </View>
            <StatusBadge 
              status={order.status} 
              color={getStatusColor(order.status)}
              icon={getStatusIcon(order.status)}
            />
          </View>
          
          <View style={styles.orderSummary}>
            <Text style={styles.totalAmount}>{formatCurrency(order.totalAmount)}</Text>
            <Text style={styles.itemCount}>
              {order.items.length} item{order.items.length !== 1 ? 's' : ''}
            </Text>
          </View>
        </Card>

        {/* Order Timeline */}
        {order.status !== 'cancelled' && (
          <Card style={styles.timelineCard} variant="elevated">
            <Text style={styles.sectionTitle}>Order Status</Text>
            <View style={styles.timeline}>
              {timeline.map((step, index) => (
                <View key={step.key} style={styles.timelineStep}>
                  <View style={styles.timelineLeft}>
                    <View style={[
                      styles.timelineIcon,
                      step.completed && styles.timelineIconCompleted,
                      step.active && styles.timelineIconActive,
                    ]}>
                      <Ionicons 
                        name={step.icon as any} 
                        size={20} 
                        color={step.completed ? '#FFFFFF' : '#CCCCCC'} 
                      />
                    </View>
                    {index < timeline.length - 1 && (
                      <View style={[
                        styles.timelineLine,
                        step.completed && styles.timelineLineCompleted,
                      ]} />
                    )}
                  </View>
                  <View style={styles.timelineRight}>
                    <Text style={[
                      styles.timelineLabel,
                      step.completed && styles.timelineLabelCompleted,
                    ]}>
                      {step.label}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </Card>
        )}

        {/* Order Items */}
        <Card style={styles.itemsCard} variant="elevated">
          <Text style={styles.sectionTitle}>Order Items</Text>
          <View style={styles.itemsList}>
            {order.items.map((item, index) => {
              const product = getProductById(item.productId);
              const vendor = getVendorById(item.vendorId);
              
              return (
                <View key={index} style={styles.orderItem}>
                  <TouchableOpacity
                    style={styles.itemContent}
                    onPress={() => handleProductPress(item.productId)}
                  >
                    <View style={styles.itemImage}>
                      <Ionicons name="image-outline" size={32} color="#CCCCCC" />
                    </View>
                    <View style={styles.itemDetails}>
                      <Text style={styles.itemName} numberOfLines={2}>
                        {item.productName}
                      </Text>
                      {vendor && (
                        <TouchableOpacity onPress={() => handleVendorPress(vendor.id)}>
                          <Text style={styles.vendorName}>by {vendor.businessName}</Text>
                        </TouchableOpacity>
                      )}
                      <View style={styles.itemPricing}>
                        <Text style={styles.itemPrice}>
                          {formatCurrency(item.price)} × {item.quantity}
                        </Text>
                        <Text style={styles.itemTotal}>
                          {formatCurrency(item.totalPrice)}
                        </Text>
                      </View>
                    </View>
                  </TouchableOpacity>
                </View>
              );
            })}
          </View>
        </Card>

        {/* Shipping Information */}
        <Card style={styles.shippingCard} variant="elevated">
          <Text style={styles.sectionTitle}>Shipping Information</Text>
          <View style={styles.shippingDetails}>
            <View style={styles.addressSection}>
              <Text style={styles.addressLabel}>Delivery Address</Text>
              <Text style={styles.addressText}>
                {order.shippingAddress.street}
              </Text>
              <Text style={styles.addressText}>
                {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
              </Text>
              <Text style={styles.addressText}>
                {order.shippingAddress.country}
              </Text>
            </View>
          </View>
        </Card>

        {/* Order Actions */}
        <View style={styles.actionsSection}>
          <Button
            title="Reorder Items"
            onPress={handleReorder}
            variant="outline"
            style={styles.actionButton}
            leftIcon={<Ionicons name="refresh-outline" size={20} color="#667eea" />}
          />
          
          {order.status === 'delivered' && (
            <Button
              title="Leave Review"
              onPress={() => Alert.alert('Feature Coming Soon', 'Review functionality will be available soon!')}
              style={styles.actionButton}
              leftIcon={<Ionicons name="star-outline" size={20} color="#FFFFFF" />}
            />
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  headerCard: {
    margin: SPACING.lg,
    padding: SPACING.lg,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: SPACING.lg,
  },
  orderInfo: {
    flex: 1,
  },
  orderId: {
    fontSize: FONT_SIZES.xl,
    fontWeight: FONT_WEIGHTS.bold,
    color: colors.text,
    marginBottom: SPACING.xs,
  },
  orderDate: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
  },
  orderSummary: {
    alignItems: 'center',
    paddingTop: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  totalAmount: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: FONT_WEIGHTS.bold,
    color: '#4CAF50',
    marginBottom: SPACING.xs,
  },
  itemCount: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
  },
  timelineCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.lg,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
    marginBottom: SPACING.lg,
  },
  timeline: {
    paddingLeft: SPACING.sm,
  },
  timelineStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  timelineLeft: {
    alignItems: 'center',
    marginRight: SPACING.lg,
  },
  timelineIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#CCCCCC',
  },
  timelineIconCompleted: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  timelineIconActive: {
    backgroundColor: '#2196F3',
    borderColor: '#2196F3',
  },
  timelineLine: {
    width: 2,
    height: 40,
    backgroundColor: '#CCCCCC',
    marginTop: SPACING.sm,
  },
  timelineLineCompleted: {
    backgroundColor: '#4CAF50',
  },
  timelineRight: {
    flex: 1,
    paddingTop: SPACING.sm,
    paddingBottom: SPACING.lg,
  },
  timelineLabel: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
  },
  timelineLabelCompleted: {
    color: colors.text,
    fontWeight: FONT_WEIGHTS.medium,
  },
  itemsCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.lg,
  },
  itemsList: {
    gap: SPACING.lg,
  },
  orderItem: {
    paddingBottom: SPACING.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  itemContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: BORDER_RADIUS.md,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  itemDetails: {
    flex: 1,
    gap: SPACING.xs,
  },
  itemName: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
  },
  vendorName: {
    fontSize: FONT_SIZES.sm,
    color: '#667eea',
    fontWeight: FONT_WEIGHTS.medium,
  },
  itemPricing: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: SPACING.sm,
  },
  itemPrice: {
    fontSize: FONT_SIZES.sm,
    color: colors.textSecondary,
  },
  itemTotal: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.semiBold,
    color: colors.text,
  },
  shippingCard: {
    marginHorizontal: SPACING.lg,
    marginBottom: SPACING.lg,
    padding: SPACING.lg,
  },
  shippingDetails: {
    gap: SPACING.lg,
  },
  addressSection: {
    gap: SPACING.xs,
  },
  addressLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: FONT_WEIGHTS.medium,
    color: colors.text,
    marginBottom: SPACING.sm,
  },
  addressText: {
    fontSize: FONT_SIZES.md,
    color: colors.textSecondary,
    lineHeight: 22,
  },
  actionsSection: {
    paddingHorizontal: SPACING.lg,
    paddingBottom: SPACING.xl,
    gap: SPACING.md,
  },
  actionButton: {
    marginBottom: SPACING.sm,
  },
});
