import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Slider from '@react-native-community/slider';
import { useThemedStyles } from '../../hooks';
import { Card } from '../Card';
import { Button } from '../Button';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
} from '../../constants/theme';
import { formatCurrency } from '../../utils';
import { PRODUCT_CATEGORIES } from '../../constants';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { ProductCategory } from '../../contexts/DataContext';

export interface FilterOptions {
  categories: ProductCategory[];
  priceRange: {
    min: number;
    max: number;
  };
  rating: number;
  inStock: boolean;
  onSale: boolean;
  vendors: string[];
  sortBy: 'relevance' | 'price_low' | 'price_high' | 'rating' | 'newest';
}

export interface FilterPanelProps {
  visible: boolean;
  onClose: () => void;
  filters: FilterOptions;
  onFiltersChange: (filters: FilterOptions) => void;
  onApply: () => void;
  onReset: () => void;
  availableVendors?: Array<{ id: string; name: string; productCount: number }>;
  priceRange?: { min: number; max: number };
}

export const FilterPanel: React.FC<FilterPanelProps> = ({
  visible,
  onClose,
  filters,
  onFiltersChange,
  onApply,
  onReset,
  availableVendors = [],
  priceRange = { min: 0, max: 1000 },
}) => {
  const styles = useThemedStyles(createStyles);
  const [tempFilters, setTempFilters] = useState<FilterOptions>(filters);

  const updateTempFilters = (updates: Partial<FilterOptions>) => {
    setTempFilters(prev => ({ ...prev, ...updates }));
  };

  const handleCategoryToggle = (category: ProductCategory) => {
    const categories = tempFilters.categories.includes(category)
      ? tempFilters.categories.filter(c => c !== category)
      : [...tempFilters.categories, category];
    updateTempFilters({ categories });
  };

  const handleVendorToggle = (vendorId: string) => {
    const vendors = tempFilters.vendors.includes(vendorId)
      ? tempFilters.vendors.filter(v => v !== vendorId)
      : [...tempFilters.vendors, vendorId];
    updateTempFilters({ vendors });
  };

  const handleApply = () => {
    onFiltersChange(tempFilters);
    onApply();
    onClose();
  };

  const handleReset = () => {
    const resetFilters: FilterOptions = {
      categories: [],
      priceRange: { min: priceRange.min, max: priceRange.max },
      rating: 0,
      inStock: false,
      onSale: false,
      vendors: [],
      sortBy: 'relevance',
    };
    setTempFilters(resetFilters);
    onFiltersChange(resetFilters);
    onReset();
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (tempFilters.categories.length > 0) count++;
    if (tempFilters.priceRange.min > priceRange.min || tempFilters.priceRange.max < priceRange.max) count++;
    if (tempFilters.rating > 0) count++;
    if (tempFilters.inStock) count++;
    if (tempFilters.onSale) count++;
    if (tempFilters.vendors.length > 0) count++;
    if (tempFilters.sortBy !== 'relevance') count++;
    return count;
  };

  const renderSection = (title: string, children: React.ReactNode) => (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );

  const renderCategories = () => (
    <View style={styles.optionsGrid}>
      {Object.values(PRODUCT_CATEGORIES).map((category) => (
        <TouchableOpacity
          key={category}
          style={[
            styles.optionChip,
            tempFilters.categories.includes(category) && styles.optionChipActive,
          ]}
          onPress={() => handleCategoryToggle(category)}
        >
          <Text
            style={[
              styles.optionChipText,
              tempFilters.categories.includes(category) && styles.optionChipTextActive,
            ]}
          >
            {category}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderPriceRange = () => (
    <View style={styles.priceRangeContainer}>
      <View style={styles.priceLabels}>
        <Text style={styles.priceLabel}>
          {formatCurrency(tempFilters.priceRange.min)}
        </Text>
        <Text style={styles.priceLabel}>
          {formatCurrency(tempFilters.priceRange.max)}
        </Text>
      </View>
      <View style={styles.slidersContainer}>
        <Text style={styles.sliderLabel}>Min Price</Text>
        <Slider
          style={styles.slider}
          minimumValue={priceRange.min}
          maximumValue={priceRange.max}
          value={tempFilters.priceRange.min}
          onValueChange={(value) =>
            updateTempFilters({
              priceRange: { ...tempFilters.priceRange, min: Math.round(value) },
            })
          }
          minimumTrackTintColor="#667eea"
          maximumTrackTintColor="#CCCCCC"
          thumbStyle={styles.sliderThumb}
        />
        <Text style={styles.sliderLabel}>Max Price</Text>
        <Slider
          style={styles.slider}
          minimumValue={priceRange.min}
          maximumValue={priceRange.max}
          value={tempFilters.priceRange.max}
          onValueChange={(value) =>
            updateTempFilters({
              priceRange: { ...tempFilters.priceRange, max: Math.round(value) },
            })
          }
          minimumTrackTintColor="#667eea"
          maximumTrackTintColor="#CCCCCC"
          thumbStyle={styles.sliderThumb}
        />
      </View>
    </View>
  );

  const renderRating = () => (
    <View style={styles.ratingContainer}>
      {[1, 2, 3, 4, 5].map((rating) => (
        <TouchableOpacity
          key={rating}
          style={styles.ratingOption}
          onPress={() => updateTempFilters({ rating })}
        >
          <View style={styles.stars}>
            {[1, 2, 3, 4, 5].map((star) => (
              <Ionicons
                key={star}
                name="star"
                size={16}
                color={star <= rating ? "#FFD700" : "#CCCCCC"}
              />
            ))}
          </View>
          <Text style={styles.ratingText}>& up</Text>
          {tempFilters.rating === rating && (
            <Ionicons name="checkmark-circle" size={20} color="#4CAF50" />
          )}
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderToggleOptions = () => (
    <View style={styles.toggleContainer}>
      <TouchableOpacity
        style={styles.toggleOption}
        onPress={() => updateTempFilters({ inStock: !tempFilters.inStock })}
      >
        <View style={styles.toggleContent}>
          <Ionicons name="cube-outline" size={20} color="#667eea" />
          <Text style={styles.toggleText}>In Stock Only</Text>
        </View>
        <View style={[styles.toggle, tempFilters.inStock && styles.toggleActive]}>
          <View style={[styles.toggleThumb, tempFilters.inStock && styles.toggleThumbActive]} />
        </View>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.toggleOption}
        onPress={() => updateTempFilters({ onSale: !tempFilters.onSale })}
      >
        <View style={styles.toggleContent}>
          <Ionicons name="pricetag-outline" size={20} color="#667eea" />
          <Text style={styles.toggleText}>On Sale</Text>
        </View>
        <View style={[styles.toggle, tempFilters.onSale && styles.toggleActive]}>
          <View style={[styles.toggleThumb, tempFilters.onSale && styles.toggleThumbActive]} />
        </View>
      </TouchableOpacity>
    </View>
  );

  const renderVendors = () => (
    <View style={styles.vendorsContainer}>
      {availableVendors.slice(0, 6).map((vendor) => (
        <TouchableOpacity
          key={vendor.id}
          style={[
            styles.vendorChip,
            tempFilters.vendors.includes(vendor.id) && styles.vendorChipActive,
          ]}
          onPress={() => handleVendorToggle(vendor.id)}
        >
          <Text
            style={[
              styles.vendorChipText,
              tempFilters.vendors.includes(vendor.id) && styles.vendorChipTextActive,
            ]}
          >
            {vendor.name}
          </Text>
          <Text style={styles.vendorProductCount}>({vendor.productCount})</Text>
        </TouchableOpacity>
      ))}
    </View>
  );

  const renderSortBy = () => {
    const sortOptions = [
      { value: 'relevance', label: 'Relevance' },
      { value: 'price_low', label: 'Price: Low to High' },
      { value: 'price_high', label: 'Price: High to Low' },
      { value: 'rating', label: 'Customer Rating' },
      { value: 'newest', label: 'Newest First' },
    ];

    return (
      <View style={styles.sortContainer}>
        {sortOptions.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.sortOption,
              tempFilters.sortBy === option.value && styles.sortOptionActive,
            ]}
            onPress={() => updateTempFilters({ sortBy: option.value as any })}
          >
            <Text
              style={[
                styles.sortOptionText,
                tempFilters.sortBy === option.value && styles.sortOptionTextActive,
              ]}
            >
              {option.label}
            </Text>
            {tempFilters.sortBy === option.value && (
              <Ionicons name="checkmark" size={16} color="#FFFFFF" />
            )}
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="#667eea" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Filters</Text>
          <TouchableOpacity onPress={handleReset}>
            <Text style={styles.resetText}>Reset</Text>
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {renderSection('Categories', renderCategories())}
          {renderSection('Price Range', renderPriceRange())}
          {renderSection('Customer Rating', renderRating())}
          {renderSection('Availability', renderToggleOptions())}
          {availableVendors.length > 0 && renderSection('Vendors', renderVendors())}
          {renderSection('Sort By', renderSortBy())}
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <Button
            title="Clear All"
            onPress={handleReset}
            variant="outline"
            style={styles.clearButton}
          />
          <Button
            title={`Apply${getActiveFiltersCount() > 0 ? ` (${getActiveFiltersCount()})` : ''}`}
            onPress={handleApply}
            style={styles.applyButton}
          />
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: colors.textPrimary,
    },
    resetText: {
      fontSize: FONT_SIZES.md,
      color: '#667eea',
      fontWeight: FONT_WEIGHTS.medium,
    },
    content: {
      flex: 1,
      paddingHorizontal: SPACING.lg,
    },
    section: {
      marginVertical: SPACING.lg,
    },
    sectionTitle: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.md,
    },
    optionsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: SPACING.sm,
    },
    optionChip: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    optionChipActive: {
      backgroundColor: '#667eea',
      borderColor: '#667eea',
    },
    optionChipText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    optionChipTextActive: {
      color: '#FFFFFF',
    },
    priceRangeContainer: {
      marginVertical: SPACING.sm,
    },
    priceLabels: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: SPACING.sm,
    },
    priceLabel: {
      fontSize: FONT_SIZES.lg,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#667eea',
    },
    slidersContainer: {
      marginVertical: SPACING.sm,
    },
    sliderLabel: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.xs,
    },
    slider: {
      width: '100%',
      height: 40,
      marginBottom: SPACING.md,
    },
    sliderThumb: {
      backgroundColor: '#667eea',
      width: 20,
      height: 20,
    },
    ratingContainer: {
      gap: SPACING.sm,
    },
    ratingOption: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: SPACING.sm,
      paddingHorizontal: SPACING.md,
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: colors.surface,
    },
    stars: {
      flexDirection: 'row',
      marginRight: SPACING.sm,
    },
    ratingText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      flex: 1,
    },
    toggleContainer: {
      gap: SPACING.md,
    },
    toggleOption: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.sm,
    },
    toggleContent: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    toggleText: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
      marginLeft: SPACING.sm,
    },
    toggle: {
      width: 50,
      height: 30,
      borderRadius: 15,
      backgroundColor: colors.border,
      justifyContent: 'center',
      padding: 2,
    },
    toggleActive: {
      backgroundColor: '#4CAF50',
    },
    toggleThumb: {
      width: 26,
      height: 26,
      borderRadius: 13,
      backgroundColor: '#FFFFFF',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    toggleThumbActive: {
      transform: [{ translateX: 20 }],
    },
    vendorsContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: SPACING.sm,
    },
    vendorChip: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    vendorChipActive: {
      backgroundColor: '#667eea',
      borderColor: '#667eea',
    },
    vendorChipText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textPrimary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    vendorChipTextActive: {
      color: '#FFFFFF',
    },
    vendorProductCount: {
      fontSize: FONT_SIZES.xs,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    sortContainer: {
      gap: SPACING.sm,
    },
    sortOption: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingVertical: SPACING.md,
      paddingHorizontal: SPACING.md,
      borderRadius: BORDER_RADIUS.md,
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    sortOptionActive: {
      backgroundColor: '#667eea',
      borderColor: '#667eea',
    },
    sortOptionText: {
      fontSize: FONT_SIZES.md,
      color: colors.textPrimary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    sortOptionTextActive: {
      color: '#FFFFFF',
    },
    footer: {
      flexDirection: 'row',
      paddingHorizontal: SPACING.lg,
      paddingVertical: SPACING.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
      gap: SPACING.md,
    },
    clearButton: {
      flex: 1,
    },
    applyButton: {
      flex: 2,
    },
  });
