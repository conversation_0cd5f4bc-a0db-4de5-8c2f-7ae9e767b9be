import React, { useRef, useEffect } from 'react';
import {
  TouchableOpacity,
  Animated,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Platform,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { useThemedStyles } from '../../hooks';
import { Button, ButtonProps } from '../Button';
import { webCompatibleHaptics } from '../../utils/webCompatibility';
import {
  SPACING,
  BORDER_RADIUS,
} from '../../constants/theme';
import type { ThemeColors } from '../../contexts/ThemeContext';

export interface AnimatedButtonProps extends ButtonProps {
  animationType?: 'scale' | 'bounce' | 'pulse' | 'shake' | 'glow';
  hapticFeedback?: 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error' | 'none';
  animationDuration?: number;
  disabled?: boolean;
  onAnimationComplete?: () => void;
}

export const AnimatedButton: React.FC<AnimatedButtonProps> = ({
  animationType = 'scale',
  hapticFeedback = 'light',
  animationDuration = 150,
  disabled = false,
  onPress,
  onAnimationComplete,
  style,
  ...buttonProps
}) => {
  const styles = useThemedStyles(createStyles);
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;

  const triggerHapticFeedback = () => {
    if (hapticFeedback === 'none' || disabled) return;

    switch (hapticFeedback) {
      case 'light':
        webCompatibleHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        break;
      case 'medium':
        webCompatibleHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        break;
      case 'heavy':
        webCompatibleHaptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
        break;
      case 'success':
        webCompatibleHaptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        break;
      case 'warning':
        webCompatibleHaptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
        break;
      case 'error':
        webCompatibleHaptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        break;
    }
  };

  const playAnimation = () => {
    if (disabled) return;

    switch (animationType) {
      case 'scale':
        playScaleAnimation();
        break;
      case 'bounce':
        playBounceAnimation();
        break;
      case 'pulse':
        playPulseAnimation();
        break;
      case 'shake':
        playShakeAnimation();
        break;
      case 'glow':
        playGlowAnimation();
        break;
    }
  };

  const playScaleAnimation = () => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: animationDuration / 2,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: animationDuration / 2,
        useNativeDriver: true,
      }),
    ]).start(onAnimationComplete);
  };

  const playBounceAnimation = () => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.9,
        duration: animationDuration / 3,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1.05,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 300,
        friction: 10,
      }),
    ]).start(onAnimationComplete);
  };

  const playPulseAnimation = () => {
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 1.1,
        duration: animationDuration,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: animationDuration,
        useNativeDriver: true,
      }),
    ]).start(onAnimationComplete);
  };

  const playShakeAnimation = () => {
    const shakeAnimation = Animated.sequence([
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: -1,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: 1,
        duration: 50,
        useNativeDriver: true,
      }),
      Animated.timing(rotateAnim, {
        toValue: 0,
        duration: 50,
        useNativeDriver: true,
      }),
    ]);

    shakeAnimation.start(onAnimationComplete);
  };

  const playGlowAnimation = () => {
    Animated.sequence([
      Animated.timing(glowAnim, {
        toValue: 1,
        duration: animationDuration,
        useNativeDriver: false,
      }),
      Animated.timing(glowAnim, {
        toValue: 0,
        duration: animationDuration,
        useNativeDriver: false,
      }),
    ]).start(onAnimationComplete);
  };

  const handlePress = () => {
    triggerHapticFeedback();
    playAnimation();
    onPress?.();
  };

  const animatedStyle = {
    transform: [
      { scale: scaleAnim },
      {
        rotate: rotateAnim.interpolate({
          inputRange: [-1, 1],
          outputRange: ['-2deg', '2deg'],
        }),
      },
    ],
    opacity: disabled ? 0.6 : opacityAnim,
    shadowOpacity: animationType === 'glow' ? glowAnim : undefined,
    elevation: animationType === 'glow' ? 
      glowAnim.interpolate({
        inputRange: [0, 1],
        outputRange: [2, 8],
      }) : undefined,
  };

  return (
    <Animated.View style={[animatedStyle, style]}>
      <Button
        {...buttonProps}
        onPress={handlePress}
        disabled={disabled}
        style={[
          animationType === 'glow' && styles.glowButton,
          buttonProps.style,
        ]}
      />
    </Animated.View>
  );
};

// Preset animated buttons for common use cases
export const SuccessButton: React.FC<Omit<AnimatedButtonProps, 'animationType' | 'hapticFeedback'>> = (props) => (
  <AnimatedButton
    {...props}
    animationType="bounce"
    hapticFeedback="success"
  />
);

export const ErrorButton: React.FC<Omit<AnimatedButtonProps, 'animationType' | 'hapticFeedback'>> = (props) => (
  <AnimatedButton
    {...props}
    animationType="shake"
    hapticFeedback="error"
  />
);

export const PulseButton: React.FC<Omit<AnimatedButtonProps, 'animationType' | 'hapticFeedback'>> = (props) => (
  <AnimatedButton
    {...props}
    animationType="pulse"
    hapticFeedback="medium"
  />
);

export const GlowButton: React.FC<Omit<AnimatedButtonProps, 'animationType' | 'hapticFeedback'>> = (props) => (
  <AnimatedButton
    {...props}
    animationType="glow"
    hapticFeedback="light"
  />
);

// Hook for programmatic animations
export const useButtonAnimation = (animationType: AnimatedButtonProps['animationType'] = 'scale') => {
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const opacityAnim = useRef(new Animated.Value(1)).current;
  const rotateAnim = useRef(new Animated.Value(0)).current;

  const animate = React.useCallback(() => {
    switch (animationType) {
      case 'scale':
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 0.95,
            duration: 75,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 75,
            useNativeDriver: true,
          }),
        ]).start();
        break;
      case 'bounce':
        Animated.sequence([
          Animated.spring(scaleAnim, {
            toValue: 1.1,
            useNativeDriver: true,
            tension: 300,
            friction: 10,
          }),
          Animated.spring(scaleAnim, {
            toValue: 1,
            useNativeDriver: true,
            tension: 300,
            friction: 10,
          }),
        ]).start();
        break;
    }
  }, [animationType, scaleAnim]);

  const animatedStyle = {
    transform: [
      { scale: scaleAnim },
      {
        rotate: rotateAnim.interpolate({
          inputRange: [-1, 1],
          outputRange: ['-2deg', '2deg'],
        }),
      },
    ],
    opacity: opacityAnim,
  };

  return { animate, animatedStyle };
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  glowButton: {
    shadowColor: colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowRadius: 8,
    elevation: 8,
  },
});
