import React, { useRef, useEffect } from 'react';
import {
  View,
  Animated,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { useThemedStyles } from '../../hooks';
import { SPACING } from '../../constants/theme';
import type { ThemeColors } from '../../contexts/ThemeContext';

const { width: screenWidth } = Dimensions.get('window');

export interface AnimatedLoaderProps {
  type?: 'dots' | 'bars' | 'pulse' | 'wave' | 'spinner' | 'skeleton';
  size?: 'small' | 'medium' | 'large';
  color?: string;
  speed?: 'slow' | 'normal' | 'fast';
  style?: any;
}

export const AnimatedLoader: React.FC<AnimatedLoaderProps> = ({
  type = 'dots',
  size = 'medium',
  color = '#667eea',
  speed = 'normal',
  style,
}) => {
  const styles = useThemedStyles(createStyles);

  const getAnimationDuration = () => {
    switch (speed) {
      case 'slow':
        return 1500;
      case 'fast':
        return 800;
      default:
        return 1200;
    }
  };

  const getSize = () => {
    switch (size) {
      case 'small':
        return 20;
      case 'large':
        return 60;
      default:
        return 40;
    }
  };

  const renderLoader = () => {
    switch (type) {
      case 'dots':
        return <DotsLoader size={getSize()} color={color} duration={getAnimationDuration()} />;
      case 'bars':
        return <BarsLoader size={getSize()} color={color} duration={getAnimationDuration()} />;
      case 'pulse':
        return <PulseLoader size={getSize()} color={color} duration={getAnimationDuration()} />;
      case 'wave':
        return <WaveLoader size={getSize()} color={color} duration={getAnimationDuration()} />;
      case 'spinner':
        return <SpinnerLoader size={getSize()} color={color} duration={getAnimationDuration()} />;
      case 'skeleton':
        return <SkeletonLoader size={getSize()} color={color} duration={getAnimationDuration()} />;
      default:
        return <DotsLoader size={getSize()} color={color} duration={getAnimationDuration()} />;
    }
  };

  return (
    <View style={[styles.container, style]}>
      {renderLoader()}
    </View>
  );
};

// Dots Loader
const DotsLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const dot1 = useRef(new Animated.Value(0)).current;
  const dot2 = useRef(new Animated.Value(0)).current;
  const dot3 = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const createAnimation = (animValue: Animated.Value, delay: number) =>
      Animated.loop(
        Animated.sequence([
          Animated.timing(animValue, {
            toValue: 1,
            duration: duration / 3,
            delay,
            useNativeDriver: true,
          }),
          Animated.timing(animValue, {
            toValue: 0,
            duration: duration / 3,
            useNativeDriver: true,
          }),
        ])
      );

    Animated.parallel([
      createAnimation(dot1, 0),
      createAnimation(dot2, duration / 6),
      createAnimation(dot3, duration / 3),
    ]).start();
  }, [dot1, dot2, dot3, duration]);

  const dotSize = size / 4;

  return (
    <View style={styles.dotsContainer}>
      {[dot1, dot2, dot3].map((dot, index) => (
        <Animated.View
          key={index}
          style={[
            styles.dot,
            {
              width: dotSize,
              height: dotSize,
              borderRadius: dotSize / 2,
              backgroundColor: color,
              opacity: dot,
              transform: [
                {
                  scale: dot.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.8, 1.2],
                  }),
                },
              ],
            },
          ]}
        />
      ))}
    </View>
  );
};

// Bars Loader
const BarsLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const bars = useRef(Array.from({ length: 5 }, () => new Animated.Value(0.3))).current;

  useEffect(() => {
    const animations = bars.map((bar, index) =>
      Animated.loop(
        Animated.sequence([
          Animated.timing(bar, {
            toValue: 1,
            duration: duration / 5,
            delay: index * (duration / 10),
            useNativeDriver: true,
          }),
          Animated.timing(bar, {
            toValue: 0.3,
            duration: duration / 5,
            useNativeDriver: true,
          }),
        ])
      )
    );

    Animated.parallel(animations).start();
  }, [bars, duration]);

  const barWidth = size / 8;
  const barHeight = size;

  return (
    <View style={styles.barsContainer}>
      {bars.map((bar, index) => (
        <Animated.View
          key={index}
          style={[
            styles.bar,
            {
              width: barWidth,
              height: barHeight,
              backgroundColor: color,
              transform: [
                {
                  scaleY: bar,
                },
              ],
            },
          ]}
        />
      ))}
    </View>
  );
};

// Pulse Loader
const PulseLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const pulse = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulse, {
          toValue: 1,
          duration: duration / 2,
          useNativeDriver: true,
        }),
        Animated.timing(pulse, {
          toValue: 0,
          duration: duration / 2,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, [pulse, duration]);

  return (
    <Animated.View
      style={[
        styles.pulse,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: color,
          opacity: pulse.interpolate({
            inputRange: [0, 1],
            outputRange: [0.3, 1],
          }),
          transform: [
            {
              scale: pulse.interpolate({
                inputRange: [0, 1],
                outputRange: [0.8, 1.2],
              }),
            },
          ],
        },
      ]}
    />
  );
};

// Wave Loader
const WaveLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const waves = useRef(Array.from({ length: 4 }, () => new Animated.Value(0))).current;

  useEffect(() => {
    const animations = waves.map((wave, index) =>
      Animated.loop(
        Animated.timing(wave, {
          toValue: 1,
          duration: duration,
          delay: index * (duration / 4),
          useNativeDriver: true,
        })
      )
    );

    Animated.parallel(animations).start();
  }, [waves, duration]);

  return (
    <View style={styles.waveContainer}>
      {waves.map((wave, index) => (
        <Animated.View
          key={index}
          style={[
            styles.wave,
            {
              width: size + index * 10,
              height: size + index * 10,
              borderRadius: (size + index * 10) / 2,
              borderWidth: 2,
              borderColor: color,
              opacity: wave.interpolate({
                inputRange: [0, 1],
                outputRange: [1, 0],
              }),
              transform: [
                {
                  scale: wave.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, 1],
                  }),
                },
              ],
            },
          ]}
        />
      ))}
    </View>
  );
};

// Spinner Loader
const SpinnerLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const spin = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.timing(spin, {
        toValue: 1,
        duration,
        useNativeDriver: true,
      })
    ).start();
  }, [spin, duration]);

  return (
    <Animated.View
      style={[
        styles.spinner,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          borderWidth: size / 10,
          borderColor: `${color}20`,
          borderTopColor: color,
          transform: [
            {
              rotate: spin.interpolate({
                inputRange: [0, 1],
                outputRange: ['0deg', '360deg'],
              }),
            },
          ],
        },
      ]}
    />
  );
};

// Skeleton Loader
const SkeletonLoader: React.FC<{ size: number; color: string; duration: number }> = ({
  size,
  color,
  duration,
}) => {
  const shimmer = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.loop(
      Animated.timing(shimmer, {
        toValue: 1,
        duration,
        useNativeDriver: true,
      })
    ).start();
  }, [shimmer, duration]);

  return (
    <View style={[styles.skeleton, { width: size * 3, height: size }]}>
      <Animated.View
        style={[
          styles.skeletonShimmer,
          {
            backgroundColor: color,
            opacity: 0.3,
            transform: [
              {
                translateX: shimmer.interpolate({
                  inputRange: [0, 1],
                  outputRange: [-size * 3, size * 3],
                }),
              },
            ],
          },
        ]}
      />
    </View>
  );
};

const createStyles = (colors: ThemeColors) => StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  dot: {
    marginHorizontal: 2,
  },
  barsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs / 2,
  },
  bar: {
    borderRadius: 2,
  },
  pulse: {
    // Styles handled inline
  },
  waveContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  wave: {
    position: 'absolute',
  },
  spinner: {
    // Styles handled inline
  },
  skeleton: {
    backgroundColor: colors.surface,
    borderRadius: 4,
    overflow: 'hidden',
  },
  skeletonShimmer: {
    width: '100%',
    height: '100%',
    borderRadius: 4,
  },
});
