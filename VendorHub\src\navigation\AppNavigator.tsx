import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useAuth, useTheme } from '../hooks';
import { LoadingSpinner } from '../components';
import { AuthNavigator } from './AuthNavigator';
import { AdminNavigator } from './AdminNavigator';
import { VendorNavigator } from './VendorNavigator';
import { CustomerNavigator } from './CustomerNavigator';
import { USER_ROLES } from '../constants';
import { View, StyleSheet } from 'react-native';

const Stack = createStackNavigator();

export const AppNavigator: React.FC = () => {
  const { user, isLoading, isAuthenticated, isAdmin, isVendor, isCustomer, isVendorApproved } = useAuth();
  const { colors } = useTheme();

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <LoadingSpinner size="large" variant="gradient" />
      </View>
    );
  }

  const getMainNavigator = () => {
    if (!isAuthenticated || !user) {
      return <AuthNavigator />;
    }

    // Admin navigation
    if (isAdmin()) {
      return <AdminNavigator />;
    }

    // Vendor navigation
    if (isVendor()) {
      // Check if vendor is approved
      if (isVendorApproved()) {
        return <VendorNavigator />;
      } else {
        // Show pending approval screen
        return <VendorNavigator />;
      }
    }

    // Customer navigation
    if (isCustomer()) {
      return <CustomerNavigator />;
    }

    // Fallback to auth if role is not recognized
    return <AuthNavigator />;
  };

  return getMainNavigator();
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
