// Quick test script to verify BHD currency formatting
const { CURRENCY } = require('./src/constants');

// Test the currency configuration
console.log('=== BHD Currency Configuration ===');
console.log('Currency Code:', CURRENCY.CODE);
console.log('Currency Symbol:', CURRENCY.SYMBOL);
console.log('Currency Name:', CURRENCY.NAME);
console.log('Country:', CURRENCY.COUNTRY_NAME);
console.log('Locale:', CURRENCY.LOCALE);
console.log('Decimal Places:', CURRENCY.DECIMAL_PLACES);
console.log('Subunit:', CURRENCY.SUBUNIT);
console.log('Subunit Ratio:', CURRENCY.SUBUNIT_RATIO);

// Test currency formatting
console.log('\n=== Currency Formatting Tests ===');

// Test basic formatting
const testAmounts = [75.200, 112.800, 18.800, 30.100, 60.200];

testAmounts.forEach(amount => {
  try {
    const formatted = new Intl.NumberFormat(CURRENCY.LOCALE, {
      style: 'currency',
      currency: CURRENCY.CODE,
      minimumFractionDigits: CURRENCY.DECIMAL_PLACES,
      maximumFractionDigits: CURRENCY.DECIMAL_PLACES,
    }).format(amount);
    console.log(`${amount} BHD -> ${formatted}`);
  } catch (error) {
    // Fallback formatting
    const fallback = `${CURRENCY.SYMBOL} ${amount.toFixed(CURRENCY.DECIMAL_PLACES)}`;
    console.log(`${amount} BHD -> ${fallback} (fallback)`);
  }
});

console.log('\n=== Sample Product Prices in BHD ===');
console.log('Wireless Headphones: BD 75.200 (was BD 94.000)');
console.log('Smart Watch Pro: BD 112.800');
console.log('Phone Charger: BD 18.800 (was BD 26.300)');
console.log('Leather Jacket: BD 112.800 (was BD 150.400)');
console.log('Summer Dress: BD 30.100');
console.log('Tennis Racket: BD 60.200');
console.log('Hiking Backpack: BD 48.900 (was BD 60.200)');

console.log('\n=== Vendor Revenue in BHD ===');
console.log('TechGear Pro: BD 16,920');
console.log('Fashion Forward: BD 12,032');
console.log('Sports Central: BD 10,528');

console.log('\n✅ BHD Currency Implementation Complete!');
