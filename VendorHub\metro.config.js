const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Add web support for AsyncStorage
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

// Add polyfills for web
config.resolver.alias = {
  ...config.resolver.alias,
  'react-native-web': 'react-native-web',
  // Add gesture handler web compatibility
  'react-native-gesture-handler': 'react-native-gesture-handler/lib/module/web',
};

// Fix source map issues
config.symbolicator = {
  ...config.symbolicator,
  customizeFrame: (frame) => {
    // Skip frames with invalid file paths
    if (frame.file && (frame.file.includes('<anonymous>') || frame.file === '<anonymous>')) {
      return null;
    }
    return frame;
  },
};

// Transformer configuration for better web support
config.transformer = {
  ...config.transformer,
  minifierConfig: {
    ...config.transformer.minifierConfig,
    keep_fnames: true,
    mangle: {
      keep_fnames: true,
    },
  },
};

module.exports = config;
