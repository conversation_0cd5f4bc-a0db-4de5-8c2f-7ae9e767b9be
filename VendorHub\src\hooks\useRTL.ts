import { useMemo } from 'react';
import { useI18n } from './useI18n';
import { ViewStyle, TextStyle } from 'react-native';

export interface RTLStyleHelpers {
  isRTL: boolean;
  textAlign: 'left' | 'right';
  flexDirection: 'row' | 'row-reverse';
  writingDirection: 'ltr' | 'rtl';
  transformStyle: (style: ViewStyle | TextStyle) => ViewStyle | TextStyle;
  paddingHorizontal: (left: number, right: number) => ViewStyle;
  marginHorizontal: (left: number, right: number) => ViewStyle;
  positioning: (left?: number, right?: number) => ViewStyle;
}

export const useRTL = (): RTLStyleHelpers => {
  const { isRTL } = useI18n();

  const helpers = useMemo(() => {
    const textAlign = isRTL ? 'right' as const : 'left' as const;
    const flexDirection = isRTL ? 'row-reverse' as const : 'row' as const;
    const writingDirection = isRTL ? 'rtl' as const : 'ltr' as const;

    const transformStyle = (style: ViewStyle | TextStyle): ViewStyle | TextStyle => {
      if (!isRTL || !style) return style;

      const transformed = { ...style };

      // Handle text alignment
      if ('textAlign' in transformed) {
        if (transformed.textAlign === 'left') {
          transformed.textAlign = 'right';
        } else if (transformed.textAlign === 'right') {
          transformed.textAlign = 'left';
        }
      }

      // Handle flex direction
      if ('flexDirection' in transformed) {
        if (transformed.flexDirection === 'row') {
          transformed.flexDirection = 'row-reverse';
        } else if (transformed.flexDirection === 'row-reverse') {
          transformed.flexDirection = 'row';
        }
      }

      // Handle padding
      if ('paddingLeft' in transformed && 'paddingRight' in transformed) {
        const temp = transformed.paddingLeft;
        transformed.paddingLeft = transformed.paddingRight;
        transformed.paddingRight = temp;
      }

      // Handle margin
      if ('marginLeft' in transformed && 'marginRight' in transformed) {
        const temp = transformed.marginLeft;
        transformed.marginLeft = transformed.marginRight;
        transformed.marginRight = temp;
      }

      // Handle positioning
      if ('left' in transformed && 'right' in transformed) {
        const temp = transformed.left;
        transformed.left = transformed.right;
        transformed.right = temp;
      }

      return transformed;
    };

    const paddingHorizontal = (left: number, right: number): ViewStyle => ({
      paddingLeft: isRTL ? right : left,
      paddingRight: isRTL ? left : right,
    });

    const marginHorizontal = (left: number, right: number): ViewStyle => ({
      marginLeft: isRTL ? right : left,
      marginRight: isRTL ? left : right,
    });

    const positioning = (left?: number, right?: number): ViewStyle => {
      const style: ViewStyle = {};
      if (left !== undefined) {
        style[isRTL ? 'right' : 'left'] = left;
      }
      if (right !== undefined) {
        style[isRTL ? 'left' : 'right'] = right;
      }
      return style;
    };

    return {
      isRTL,
      textAlign,
      flexDirection,
      writingDirection,
      transformStyle,
      paddingHorizontal,
      marginHorizontal,
      positioning,
    };
  }, [isRTL]);

  return helpers;
};
