import React, { useState, useEffect } from 'react';
import { StyleSheet, Alert, ScrollView } from 'react-native';
import { useI18n } from '../hooks/useI18n';
import { RTLView, RTLText, RTLIcon } from './RTL';
import { Button } from './Button';
import { Card } from './Card';
import { rtlTestingFramework, RTLTestResult } from '../utils/RTLTestingUtils';
import { SPACING, FONT_SIZES, FONT_WEIGHTS } from '../constants/theme';

export const RTLTestingComponent: React.FC = () => {
  const { t, currentLanguage, isRTL, changeLanguage } = useI18n();
  const [testResults, setTestResults] = useState<RTLTestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [showResults, setShowResults] = useState(false);

  useEffect(() => {
    // Load any existing test results
    setTestResults(rtlTestingFramework.getTestResults());
  }, []);

  const runRTLTests = async () => {
    setIsRunningTests(true);
    setShowResults(false);
    
    try {
      Alert.alert(
        'RTL Testing',
        'Running comprehensive RTL test suite. This may take a few moments and will switch languages.',
        [{ text: 'OK' }]
      );

      const results = await rtlTestingFramework.runFullTestSuite();
      setTestResults(results);
      setShowResults(true);
      
      const passedTests = results.filter(r => r.passed).length;
      const totalTests = results.length;
      
      Alert.alert(
        'RTL Tests Complete',
        `Tests completed: ${passedTests}/${totalTests} passed\nSuccess rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`,
        [{ text: 'View Results', onPress: () => setShowResults(true) }]
      );
      
    } catch (error) {
      Alert.alert(
        'Test Error',
        'Failed to run RTL tests: ' + (error instanceof Error ? error.message : 'Unknown error')
      );
    } finally {
      setIsRunningTests(false);
    }
  };

  const clearTestResults = () => {
    rtlTestingFramework.clearTestResults();
    setTestResults([]);
    setShowResults(false);
  };

  const switchToArabic = async () => {
    await changeLanguage('ar');
  };

  const switchToEnglish = async () => {
    await changeLanguage('en');
  };

  const testRTLLayout = () => {
    Alert.alert(
      'RTL Layout Test',
      `Current Language: ${currentLanguage}\nIs RTL: ${isRTL}\nText Direction: ${isRTL ? 'Right to Left' : 'Left to Right'}\nLayout Direction: ${isRTL ? 'row-reverse' : 'row'}`,
      [{ text: 'Close' }]
    );
  };

  const renderTestResult = (result: RTLTestResult, index: number) => (
    <Card key={index} style={[styles.testResultCard, result.passed ? styles.passedTest : styles.failedTest]}>
      <RTLView style={styles.testResultHeader}>
        <RTLIcon 
          name={result.passed ? 'checkmark-circle' : 'close-circle'} 
          size={20} 
          color={result.passed ? '#4CAF50' : '#F44336'} 
        />
        <RTLText style={styles.testName} numberOfLines={2}>
          {result.testName}
        </RTLText>
      </RTLView>
      
      {!result.passed && result.error && (
        <RTLText style={styles.errorText}>
          Error: {result.error}
        </RTLText>
      )}
      
      {result.details && (
        <RTLText style={styles.detailsText}>
          Details: {JSON.stringify(result.details, null, 2)}
        </RTLText>
      )}
      
      <RTLText style={styles.timestampText}>
        {result.timestamp.toLocaleTimeString()}
      </RTLText>
    </Card>
  );

  const renderTestSummary = () => {
    if (testResults.length === 0) return null;

    const passedTests = testResults.filter(r => r.passed).length;
    const failedTests = testResults.length - passedTests;
    const successRate = ((passedTests / testResults.length) * 100).toFixed(1);

    return (
      <Card style={styles.summaryCard}>
        <RTLText style={styles.summaryTitle}>Test Summary</RTLText>
        <RTLView style={styles.summaryRow}>
          <RTLText style={styles.summaryLabel}>Total Tests:</RTLText>
          <RTLText style={styles.summaryValue}>{testResults.length}</RTLText>
        </RTLView>
        <RTLView style={styles.summaryRow}>
          <RTLText style={styles.summaryLabel}>Passed:</RTLText>
          <RTLText style={[styles.summaryValue, styles.passedText]}>{passedTests}</RTLText>
        </RTLView>
        <RTLView style={styles.summaryRow}>
          <RTLText style={styles.summaryLabel}>Failed:</RTLText>
          <RTLText style={[styles.summaryValue, styles.failedText]}>{failedTests}</RTLText>
        </RTLView>
        <RTLView style={styles.summaryRow}>
          <RTLText style={styles.summaryLabel}>Success Rate:</RTLText>
          <RTLText style={[styles.summaryValue, passedTests === testResults.length ? styles.passedText : styles.failedText]}>
            {successRate}%
          </RTLText>
        </RTLView>
      </Card>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Card style={styles.card}>
        <RTLText style={styles.title} weight="bold">
          RTL Testing Framework
        </RTLText>
        
        <RTLText style={styles.description}>
          Comprehensive testing for RTL layouts, translations, and navigation flow.
        </RTLText>

        {/* Current Status */}
        <Card style={styles.statusCard} variant="outlined">
          <RTLText style={styles.statusTitle} weight="medium">Current Status</RTLText>
          <RTLView style={styles.statusRow}>
            <RTLIcon name="language" size={20} color="#007AFF" />
            <RTLText style={styles.statusText}>
              Language: {currentLanguage.toUpperCase()}
            </RTLText>
          </RTLView>
          <RTLView style={styles.statusRow}>
            <RTLIcon name={isRTL ? "arrow-back" : "arrow-forward"} size={20} color="#007AFF" />
            <RTLText style={styles.statusText}>
              Direction: {isRTL ? 'RTL (Right to Left)' : 'LTR (Left to Right)'}
            </RTLText>
          </RTLView>
        </Card>

        {/* Quick Actions */}
        <RTLView style={styles.buttonContainer}>
          <Button
            title="Switch to Arabic"
            onPress={switchToArabic}
            variant="outline"
            size="small"
            style={styles.button}
            leftIcon={<RTLIcon name="language" size={16} color="#667eea" />}
          />
          
          <Button
            title="Switch to English"
            onPress={switchToEnglish}
            variant="outline"
            size="small"
            style={styles.button}
            leftIcon={<RTLIcon name="language" size={16} color="#667eea" />}
          />
        </RTLView>

        <RTLView style={styles.buttonContainer}>
          <Button
            title="Test RTL Layout"
            onPress={testRTLLayout}
            variant="outline"
            size="small"
            style={styles.button}
            leftIcon={<RTLIcon name="grid-outline" size={16} color="#667eea" />}
          />
          
          <Button
            title="Run Full Test Suite"
            onPress={runRTLTests}
            variant="primary"
            size="small"
            style={styles.button}
            disabled={isRunningTests}
            leftIcon={<RTLIcon name={isRunningTests ? "hourglass" : "play"} size={16} color="#FFFFFF" />}
          />
        </RTLView>

        {isRunningTests && (
          <RTLView style={styles.loadingContainer}>
            <RTLIcon name="hourglass" size={24} color="#667eea" />
            <RTLText style={styles.loadingText}>Running RTL tests...</RTLText>
          </RTLView>
        )}

        {/* Test Results */}
        {showResults && testResults.length > 0 && (
          <>
            <RTLView style={styles.resultsHeader}>
              <RTLText style={styles.resultsTitle} weight="bold">Test Results</RTLText>
              <Button
                title="Clear"
                onPress={clearTestResults}
                variant="outline"
                size="small"
                leftIcon={<RTLIcon name="trash-outline" size={14} color="#F44336" />}
              />
            </RTLView>

            {renderTestSummary()}

            <RTLView style={styles.testResultsContainer}>
              {testResults.map(renderTestResult)}
            </RTLView>
          </>
        )}

        {/* RTL Demo Elements */}
        <Card style={styles.demoCard} variant="outlined">
          <RTLText style={styles.demoTitle} weight="medium">RTL Demo Elements</RTLText>
          
          <RTLView style={styles.demoRow}>
            <RTLIcon name="arrow-forward" size={24} color="#007AFF" />
            <RTLText style={styles.demoText}>Forward Arrow (should flip in RTL)</RTLText>
          </RTLView>

          <RTLView style={styles.demoRow}>
            <RTLIcon name="chevron-forward" size={24} color="#007AFF" />
            <RTLText style={styles.demoText}>Chevron Forward (should flip in RTL)</RTLText>
          </RTLView>

          <RTLView style={styles.demoRow}>
            <RTLIcon name="star" size={24} color="#FFD700" />
            <RTLText style={styles.demoText}>Star (should NOT flip in RTL)</RTLText>
          </RTLView>

          <RTLView style={styles.demoRow}>
            <RTLIcon name="heart" size={24} color="#E91E63" />
            <RTLText style={styles.demoText}>Heart (should NOT flip in RTL)</RTLText>
          </RTLView>
        </Card>
      </Card>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: SPACING.md,
  },
  card: {
    padding: SPACING.lg,
  },
  title: {
    fontSize: FONT_SIZES.xl,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  description: {
    fontSize: FONT_SIZES.sm,
    textAlign: 'center',
    marginBottom: SPACING.lg,
    opacity: 0.7,
  },
  statusCard: {
    padding: SPACING.md,
    marginBottom: SPACING.md,
  },
  statusTitle: {
    fontSize: FONT_SIZES.md,
    marginBottom: SPACING.sm,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  statusText: {
    marginLeft: SPACING.sm,
    fontSize: FONT_SIZES.sm,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  button: {
    flex: 1,
    marginHorizontal: SPACING.xs,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: SPACING.md,
  },
  loadingText: {
    marginLeft: SPACING.sm,
    fontSize: FONT_SIZES.sm,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: SPACING.lg,
    marginBottom: SPACING.md,
  },
  resultsTitle: {
    fontSize: FONT_SIZES.lg,
  },
  summaryCard: {
    padding: SPACING.md,
    marginBottom: SPACING.md,
    backgroundColor: '#F5F5F5',
  },
  summaryTitle: {
    fontSize: FONT_SIZES.md,
    marginBottom: SPACING.sm,
    textAlign: 'center',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.xs,
  },
  summaryLabel: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
  },
  summaryValue: {
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.bold,
  },
  passedText: {
    color: '#4CAF50',
  },
  failedText: {
    color: '#F44336',
  },
  testResultsContainer: {
    marginTop: SPACING.sm,
  },
  testResultCard: {
    padding: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  passedTest: {
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  failedTest: {
    borderLeftWidth: 4,
    borderLeftColor: '#F44336',
  },
  testResultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.xs,
  },
  testName: {
    marginLeft: SPACING.sm,
    fontSize: FONT_SIZES.sm,
    fontWeight: FONT_WEIGHTS.medium,
    flex: 1,
  },
  errorText: {
    fontSize: FONT_SIZES.xs,
    color: '#F44336',
    marginBottom: SPACING.xs,
  },
  detailsText: {
    fontSize: FONT_SIZES.xs,
    color: '#666',
    fontFamily: 'monospace',
    marginBottom: SPACING.xs,
  },
  timestampText: {
    fontSize: FONT_SIZES.xs,
    color: '#999',
    textAlign: 'right',
  },
  demoCard: {
    padding: SPACING.md,
    marginTop: SPACING.lg,
  },
  demoTitle: {
    fontSize: FONT_SIZES.md,
    marginBottom: SPACING.sm,
  },
  demoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.sm,
  },
  demoText: {
    marginLeft: SPACING.sm,
    fontSize: FONT_SIZES.sm,
  },
});
