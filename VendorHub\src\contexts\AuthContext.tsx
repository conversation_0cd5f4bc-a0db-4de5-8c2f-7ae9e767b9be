import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { USER_ROLES, STORAGE_KEYS, DEFAULT_ADMIN } from '../constants';
import type { UserRole } from '../constants';
import { storage } from '../utils/storage';

// Types
export interface User {
  id: string;
  email: string;
  role: UserRole;
  name: string;
  avatar?: string;
  phone?: string;
  address?: string;
  isApproved?: boolean;
  businessName?: string;
  businessDescription?: string;
  businessLogo?: string;
  createdAt: string;
}

export interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  token: string | null;
}

type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'LOGIN_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: Partial<User> }
  | { type: 'RESTORE_SESSION'; payload: { user: User; token: string } };

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<{ success: boolean; message?: string }>;
  logout: () => Promise<void>;
  register: (userData: RegisterData) => Promise<{ success: boolean; message?: string }>;
  updateUser: (userData: Partial<User>) => Promise<void>;
  loginAsVendor: () => Promise<{ success: boolean; message?: string }>;
  isAdmin: () => boolean;
  isVendor: () => boolean;
  isCustomer: () => boolean;
  isVendorApproved: () => boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role: UserRole;
  businessName?: string;
  businessDescription?: string;
  phone?: string;
}

// Initial state
const initialState: AuthState = {
  user: null,
  isLoading: true,
  isAuthenticated: false,
  token: null,
};

// Reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
      };
    
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
      };
    
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };
    
    case 'RESTORE_SESSION':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
      };
    
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Restore session on app start
  useEffect(() => {
    restoreSession();
  }, []);

  const restoreSession = async () => {
    try {
      const [storedToken, storedUser] = await Promise.all([
        storage.getItem(STORAGE_KEYS.USER_TOKEN),
        storage.getItem(STORAGE_KEYS.USER_DATA),
      ]);

      if (storedToken && storedUser) {
        const user = JSON.parse(storedUser);
        dispatch({
          type: 'RESTORE_SESSION',
          payload: { user, token: storedToken },
        });
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    } catch (error) {
      console.error('Error restoring session:', error);
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const login = async (email: string, password: string): Promise<{ success: boolean; message?: string }> => {
    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check admin credentials
      if (email === DEFAULT_ADMIN.email && password === DEFAULT_ADMIN.password) {
        const adminUser: User = {
          id: 'admin-1',
          email: DEFAULT_ADMIN.email,
          role: USER_ROLES.ADMIN,
          name: 'Admin User',
          avatar: undefined,
          createdAt: new Date().toISOString(),
        };

        const token = 'admin-token-' + Date.now();

        // Store session
        await storage.setItem(STORAGE_KEYS.USER_TOKEN, token);
        await storage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(adminUser));

        dispatch({
          type: 'LOGIN_SUCCESS',
          payload: { user: adminUser, token },
        });

        return { success: true };
      }

      // Mock vendor/customer login (for demo purposes)
      // In a real app, this would be an API call
      const mockUser: User = {
        id: 'user-' + Date.now(),
        email,
        role: email.includes('vendor') ? USER_ROLES.VENDOR : USER_ROLES.CUSTOMER,
        name: email.split('@')[0],
        isApproved: email.includes('vendor') ? true : undefined,
        businessName: email.includes('vendor') ? `${email.split('@')[0]} Business` : undefined,
        createdAt: new Date().toISOString(),
      };

      const token = 'user-token-' + Date.now();

      // Store session
      await storage.setItem(STORAGE_KEYS.USER_TOKEN, token);
      await storage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(mockUser));

      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { user: mockUser, token },
      });

      return { success: true };
    } catch (error) {
      dispatch({ type: 'SET_LOADING', payload: false });
      return { success: false, message: 'Login failed. Please try again.' };
    }
  };

  const loginAsVendor = async (): Promise<{ success: boolean; message?: string }> => {
    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create mock vendor user data
      const vendorUser: User = {
        id: 'vendor-' + Date.now(),
        email: '<EMAIL>',
        role: USER_ROLES.VENDOR,
        name: 'Demo Vendor',
        avatar: undefined,
        phone: '+****************',
        address: '123 Business St, Commerce City, CC 12345',
        isApproved: true, // Set to true for approved vendor
        businessName: 'Demo Electronics Store',
        businessDescription: 'Your trusted source for quality electronics and gadgets. We offer the latest technology products with excellent customer service and competitive prices.',
        businessLogo: undefined,
        createdAt: new Date().toISOString(),
      };

      const token = 'vendor-token-' + Date.now();

      // Store session in AsyncStorage
      await storage.setItem(STORAGE_KEYS.USER_TOKEN, token);
      await storage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(vendorUser));

      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { user: vendorUser, token },
      });

      return { success: true };
    } catch (error) {
      dispatch({ type: 'SET_LOADING', payload: false });
      return { success: false, message: 'Vendor login failed. Please try again.' };
    }
  };

  const register = async (userData: RegisterData): Promise<{ success: boolean; message?: string }> => {
    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      const newUser: User = {
        id: 'user-' + Date.now(),
        email: userData.email,
        role: userData.role,
        name: userData.name,
        isApproved: userData.role === USER_ROLES.VENDOR ? false : undefined,
        businessName: userData.businessName,
        businessDescription: userData.businessDescription,
        createdAt: new Date().toISOString(),
      };

      // For vendors, they need approval, so don't auto-login
      if (userData.role === USER_ROLES.VENDOR) {
        dispatch({ type: 'SET_LOADING', payload: false });
        return { 
          success: true, 
          message: 'Registration successful! Please wait for admin approval.' 
        };
      }

      // For customers, auto-login
      const token = 'user-token-' + Date.now();

      await storage.setItem(STORAGE_KEYS.USER_TOKEN, token);
      await storage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(newUser));

      dispatch({
        type: 'LOGIN_SUCCESS',
        payload: { user: newUser, token },
      });

      return { success: true };
    } catch (error) {
      dispatch({ type: 'SET_LOADING', payload: false });
      return { success: false, message: 'Registration failed. Please try again.' };
    }
  };

  const logout = async () => {
    try {
      await Promise.all([
        storage.removeItem(STORAGE_KEYS.USER_TOKEN),
        storage.removeItem(STORAGE_KEYS.USER_DATA),
      ]);
      dispatch({ type: 'LOGOUT' });
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  const updateUser = async (userData: Partial<User>) => {
    if (state.user) {
      const updatedUser = { ...state.user, ...userData };
      await storage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(updatedUser));
      dispatch({ type: 'UPDATE_USER', payload: userData });
    }
  };

  const isAdmin = () => state.user?.role === USER_ROLES.ADMIN;
  const isVendor = () => state.user?.role === USER_ROLES.VENDOR;
  const isCustomer = () => state.user?.role === USER_ROLES.CUSTOMER;
  const isVendorApproved = () => isVendor() && state.user?.isApproved === true;

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    register,
    updateUser,
    loginAsVendor,
    isAdmin,
    isVendor,
    isCustomer,
    isVendorApproved,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
