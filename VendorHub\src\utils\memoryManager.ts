import React from 'react';
import { InteractionManager, AppState, AppStateStatus } from 'react-native';

// Memory management utilities for optimizing app performance

export class MemoryManager {
  private static instance: MemoryManager;
  private imageCache = new Map<string, { uri: string; timestamp: number; size: number }>();
  private dataCache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private maxCacheSize = 50 * 1024 * 1024; // 50MB
  private currentCacheSize = 0;
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.startCleanupInterval();
    this.setupAppStateListener();
  }

  public static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  // Image cache management
  public cacheImage(uri: string, size: number = 1024): void {
    if (this.currentCacheSize + size > this.maxCacheSize) {
      this.cleanupImageCache();
    }

    this.imageCache.set(uri, {
      uri,
      timestamp: Date.now(),
      size,
    });
    this.currentCacheSize += size;
  }

  public getImageFromCache(uri: string): string | null {
    const cached = this.imageCache.get(uri);
    if (cached) {
      // Update timestamp for LRU
      cached.timestamp = Date.now();
      return cached.uri;
    }
    return null;
  }

  public removeImageFromCache(uri: string): void {
    const cached = this.imageCache.get(uri);
    if (cached) {
      this.currentCacheSize -= cached.size;
      this.imageCache.delete(uri);
    }
  }

  private cleanupImageCache(): void {
    // Remove oldest 25% of cached images
    const entries = Array.from(this.imageCache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    const toRemove = Math.ceil(entries.length * 0.25);
    for (let i = 0; i < toRemove; i++) {
      const [uri, data] = entries[i];
      this.currentCacheSize -= data.size;
      this.imageCache.delete(uri);
    }
  }

  // Data cache management with TTL
  public cacheData(key: string, data: any, ttlMs: number = 5 * 60 * 1000): void {
    this.dataCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlMs,
    });
  }

  public getDataFromCache<T>(key: string): T | null {
    const cached = this.dataCache.get(key);
    if (cached) {
      const now = Date.now();
      if (now - cached.timestamp < cached.ttl) {
        return cached.data as T;
      } else {
        this.dataCache.delete(key);
      }
    }
    return null;
  }

  public invalidateDataCache(keyPattern?: string): void {
    if (keyPattern) {
      const regex = new RegExp(keyPattern);
      for (const key of this.dataCache.keys()) {
        if (regex.test(key)) {
          this.dataCache.delete(key);
        }
      }
    } else {
      this.dataCache.clear();
    }
  }

  // Memory cleanup
  public forceCleanup(): void {
    this.cleanupImageCache();
    this.cleanupDataCache();
    
    // Force garbage collection if available (development only)
    if (__DEV__ && global.gc) {
      global.gc();
    }
  }

  private cleanupDataCache(): void {
    const now = Date.now();
    for (const [key, cached] of this.dataCache.entries()) {
      if (now - cached.timestamp >= cached.ttl) {
        this.dataCache.delete(key);
      }
    }
  }

  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupDataCache();
      
      // Cleanup image cache if it's getting too large
      if (this.currentCacheSize > this.maxCacheSize * 0.8) {
        this.cleanupImageCache();
      }
    }, 60000); // Every minute
  }

  private setupAppStateListener(): void {
    AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      if (nextAppState === 'background') {
        // Aggressive cleanup when app goes to background
        this.forceCleanup();
      }
    });
  }

  public getCacheStats(): {
    imageCacheSize: number;
    imageCacheCount: number;
    dataCacheCount: number;
    maxCacheSize: number;
  } {
    return {
      imageCacheSize: this.currentCacheSize,
      imageCacheCount: this.imageCache.size,
      dataCacheCount: this.dataCache.size,
      maxCacheSize: this.maxCacheSize,
    };
  }
}

// Utility functions for performance optimization

export const runAfterInteractions = (callback: () => void): void => {
  InteractionManager.runAfterInteractions(callback);
};

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

// Memoization utility with size limit
export class MemoCache<K, V> {
  private cache = new Map<K, V>();
  private maxSize: number;

  constructor(maxSize: number = 100) {
    this.maxSize = maxSize;
  }

  get(key: K): V | undefined {
    const value = this.cache.get(key);
    if (value !== undefined) {
      // Move to end (LRU)
      this.cache.delete(key);
      this.cache.set(key, value);
    }
    return value;
  }

  set(key: K, value: V): void {
    if (this.cache.has(key)) {
      this.cache.delete(key);
    } else if (this.cache.size >= this.maxSize) {
      // Remove oldest entry
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, value);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

// React hook for memoization with automatic cleanup
export const useMemoCache = <K, V>(maxSize: number = 100) => {
  const cacheRef = React.useRef(new MemoCache<K, V>(maxSize));
  
  React.useEffect(() => {
    return () => {
      cacheRef.current.clear();
    };
  }, []);
  
  return cacheRef.current;
};

// Performance monitoring utilities
export const measurePerformance = (name: string, fn: () => void): void => {
  if (__DEV__) {
    const start = performance.now();
    fn();
    const end = performance.now();
    console.log(`[Performance] ${name}: ${end - start}ms`);
  } else {
    fn();
  }
};

export const measureAsyncPerformance = async (
  name: string,
  fn: () => Promise<void>
): Promise<void> => {
  if (__DEV__) {
    const start = performance.now();
    await fn();
    const end = performance.now();
    console.log(`[Performance] ${name}: ${end - start}ms`);
  } else {
    await fn();
  }
};

// Memory usage monitoring (development only)
export const logMemoryUsage = (context: string): void => {
  if (__DEV__ && global.performance && global.performance.memory) {
    const memory = global.performance.memory;
    console.log(`[Memory] ${context}:`, {
      used: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`,
      total: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)}MB`,
      limit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB`,
    });
  }
};

// Batch processing utility for large datasets
export const processBatch = async <T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  batchSize: number = 10,
  delayMs: number = 0
): Promise<R[]> => {
  const results: R[] = [];
  
  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await Promise.all(batch.map(processor));
    results.push(...batchResults);
    
    // Add delay between batches to prevent blocking
    if (delayMs > 0 && i + batchSize < items.length) {
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }
  
  return results;
};

// Export singleton instance
export const memoryManager = MemoryManager.getInstance();
