import React from 'react';
import { ViewStyle } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useI18n } from '../../hooks/useI18n';

interface RTLIconProps {
  name: keyof typeof Ionicons.glyphMap;
  size?: number;
  color?: string;
  style?: ViewStyle | ViewStyle[];
}

export const RTLIcon: React.FC<RTLIconProps> = ({ name, style, ...props }) => {
  const { isRTL } = useI18n();
  
  // List of icons that should be flipped in RTL mode
  const iconsToFlip = [
    'arrow-back',
    'arrow-forward', 
    'chevron-back',
    'chevron-forward',
    'return-up-back',
    'return-up-forward',
    'caret-back',
    'caret-forward',
    'play-back',
    'play-forward',
    'skip-back',
    'skip-forward',
    'arrow-back-circle',
    'arrow-forward-circle',
    'chevron-back-circle',
    'chevron-forward-circle',
    'arrow-back-outline',
    'arrow-forward-outline',
    'chevron-back-outline',
    'chevron-forward-outline',
    'arrow-back-circle-outline',
    'arrow-forward-circle-outline',
    'chevron-back-circle-outline',
    'chevron-forward-circle-outline',
    'caret-back-outline',
    'caret-forward-outline',
    'play-back-outline',
    'play-forward-outline',
    'skip-back-outline',
    'skip-forward-outline',
    'arrow-undo',
    'arrow-redo',
    'arrow-undo-outline',
    'arrow-redo-outline',
    'exit',
    'exit-outline',
    'enter',
    'enter-outline',
    'log-in',
    'log-in-outline',
    'log-out',
    'log-out-outline',
    'trending-up',
    'trending-down',
    'share',
    'share-outline',
    'send',
    'send-outline',
    'paper-plane',
    'paper-plane-outline'
  ];
  
  const shouldFlip = iconsToFlip.includes(name);
  
  const iconStyle = [
    style,
    shouldFlip && isRTL && { transform: [{ scaleX: -1 }] }
  ].filter(Boolean);
  
  return (
    <Ionicons
      name={name}
      style={iconStyle}
      {...props}
    />
  );
};
