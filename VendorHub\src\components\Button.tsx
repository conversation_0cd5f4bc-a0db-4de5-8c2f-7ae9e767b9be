import React from 'react';
import {
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useThemedStyles, useI18n } from '../hooks';
import { RTLView, RTLText } from './RTL';
import {
  BUTTON_HEIGHT,
  BORDER_RADIUS,
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  SHADOWS,
  GRADIENTS,
} from '../constants/theme';
import type { ThemeColors } from '../contexts/ThemeContext';

export interface ButtonProps extends TouchableOpacityProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  loading = false,
  disabled = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  style,
  textStyle,
  ...props
}) => {
  const styles = useThemedStyles(createStyles);
  const { isRTL } = useI18n();

  const isDisabled = disabled || loading;

  const buttonStyle = [
    styles.base,
    styles[variant],
    styles[size],
    fullWidth && styles.fullWidth,
    isDisabled && styles.disabled,
    style,
  ];

  const textStyles = [
    styles.text,
    styles[`${variant}Text`],
    styles[`${size}Text`],
    isDisabled && styles.disabledText,
    textStyle,
  ];

  // Determine icon positions based on RTL
  const startIcon = isRTL ? rightIcon : leftIcon;
  const endIcon = isRTL ? leftIcon : rightIcon;

  const renderContent = () => (
    <RTLView style={styles.contentContainer}>
      {startIcon && !loading && startIcon}
      {loading ? (
        <ActivityIndicator
          size="small"
          color={variant === 'primary' ? '#FFFFFF' : styles.text.color}
          style={styles.loader}
        />
      ) : (
        <RTLText style={textStyles}>{title}</RTLText>
      )}
      {endIcon && !loading && endIcon}
    </RTLView>
  );

  if (variant === 'primary' && !isDisabled) {
    return (
      <TouchableOpacity
        style={[buttonStyle, { padding: 0 }]}
        onPress={onPress}
        disabled={isDisabled}
        activeOpacity={0.8}
        {...props}
      >
        <LinearGradient
          colors={GRADIENTS.primary}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[styles.gradient, styles[size]]}
        >
          {renderContent()}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={isDisabled}
      activeOpacity={0.8}
      {...props}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    base: {
      borderRadius: BORDER_RADIUS.md,
      alignItems: 'center',
      justifyContent: 'center',
      // Use individual shadow properties instead of ...SHADOWS.small
      elevation: 2,
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.8,
      shadowRadius: 1,
    },
    contentContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
    },
    gradient: {
      borderRadius: BORDER_RADIUS.md,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
    },
    primary: {
      backgroundColor: colors.primary,
    },
    secondary: {
      backgroundColor: colors.backgroundSecondary,
      borderWidth: 1,
      borderColor: colors.border,
    },
    outline: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: colors.primary,
    },
    ghost: {
      backgroundColor: 'transparent',
    },
    danger: {
      backgroundColor: colors.error,
    },
    small: {
      height: BUTTON_HEIGHT * 0.8,
      paddingHorizontal: SPACING.md,
    },
    medium: {
      height: BUTTON_HEIGHT,
      paddingHorizontal: SPACING.lg,
    },
    large: {
      height: BUTTON_HEIGHT * 1.2,
      paddingHorizontal: SPACING.xl,
    },
    fullWidth: {
      width: '100%',
    },
    disabled: {
      opacity: 0.5,
    },
    text: {
      fontWeight: FONT_WEIGHTS.medium,
      textAlign: 'center',
    },
    primaryText: {
      color: colors.textOnPrimary,
      fontSize: FONT_SIZES.md,
    },
    secondaryText: {
      color: colors.textPrimary,
      fontSize: FONT_SIZES.md,
    },
    outlineText: {
      color: colors.primary,
      fontSize: FONT_SIZES.md,
    },
    ghostText: {
      color: colors.primary,
      fontSize: FONT_SIZES.md,
    },
    dangerText: {
      color: colors.textOnPrimary,
      fontSize: FONT_SIZES.md,
    },
    smallText: {
      fontSize: FONT_SIZES.sm,
    },
    mediumText: {
      fontSize: FONT_SIZES.md,
    },
    largeText: {
      fontSize: FONT_SIZES.lg,
    },
    disabledText: {
      color: colors.textDisabled,
    },
    loader: {
      marginRight: SPACING.xs,
    },
  });
