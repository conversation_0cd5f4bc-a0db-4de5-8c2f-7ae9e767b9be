import { Platform } from 'react-native';

// Platform-safe storage interface
interface StorageInterface {
  getItem(key: string): Promise<string | null>;
  setItem(key: string, value: string): Promise<void>;
  removeItem(key: string): Promise<void>;
  clear(): Promise<void>;
}

// Web storage implementation
class WebStorage implements StorageInterface {
  private isAvailable(): boolean {
    try {
      return typeof window !== 'undefined' && window.localStorage !== undefined;
    } catch {
      return false;
    }
  }

  async getItem(key: string): Promise<string | null> {
    if (!this.isAvailable()) {
      console.warn('localStorage is not available, returning null');
      return null;
    }
    try {
      return window.localStorage.getItem(key);
    } catch (error) {
      console.error('Error getting item from localStorage:', error);
      return null;
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    if (!this.isAvailable()) {
      console.warn('localStorage is not available, skipping setItem');
      return;
    }
    try {
      window.localStorage.setItem(key, value);
    } catch (error) {
      console.error('Error setting item in localStorage:', error);
    }
  }

  async removeItem(key: string): Promise<void> {
    if (!this.isAvailable()) {
      console.warn('localStorage is not available, skipping removeItem');
      return;
    }
    try {
      window.localStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing item from localStorage:', error);
    }
  }

  async clear(): Promise<void> {
    if (!this.isAvailable()) {
      console.warn('localStorage is not available, skipping clear');
      return;
    }
    try {
      window.localStorage.clear();
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  }
}

// Memory storage fallback
class MemoryStorage implements StorageInterface {
  private storage: Map<string, string> = new Map();

  async getItem(key: string): Promise<string | null> {
    return this.storage.get(key) || null;
  }

  async setItem(key: string, value: string): Promise<void> {
    this.storage.set(key, value);
  }

  async removeItem(key: string): Promise<void> {
    this.storage.delete(key);
  }

  async clear(): Promise<void> {
    this.storage.clear();
  }
}

// Create platform-specific storage instance
function createStorage(): StorageInterface {
  if (Platform.OS === 'web') {
    // For web, try localStorage first, fallback to memory
    const webStorage = new WebStorage();
    if (typeof window !== 'undefined' && window.localStorage) {
      return webStorage;
    } else {
      console.warn('localStorage not available, using memory storage');
      return new MemoryStorage();
    }
  } else {
    // For native platforms, use AsyncStorage
    try {
      const AsyncStorage = require('@react-native-async-storage/async-storage').default;
      return AsyncStorage;
    } catch (error) {
      console.error('AsyncStorage not available, using memory storage:', error);
      return new MemoryStorage();
    }
  }
}

// Export the storage instance
export const storage = createStorage();

// Helper functions for common operations
export const StorageUtils = {
  async getObject<T>(key: string): Promise<T | null> {
    try {
      const value = await storage.getItem(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error(`Error getting object for key ${key}:`, error);
      return null;
    }
  },

  async setObject<T>(key: string, value: T): Promise<void> {
    try {
      await storage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error setting object for key ${key}:`, error);
    }
  },

  async removeItem(key: string): Promise<void> {
    try {
      await storage.removeItem(key);
    } catch (error) {
      console.error(`Error removing item for key ${key}:`, error);
    }
  },

  async clear(): Promise<void> {
    try {
      await storage.clear();
    } catch (error) {
      console.error('Error clearing storage:', error);
    }
  },
};
