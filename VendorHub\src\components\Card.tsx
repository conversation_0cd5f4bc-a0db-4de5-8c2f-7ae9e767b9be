import React from 'react';
import {
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TouchableOpacityProps,
} from 'react-native';
import { useThemedStyles } from '../hooks';
import { RTLView } from './RTL';
import { SHADOWS, BORDER_RADIUS, SPACING } from '../constants/theme';
import type { ThemeColors } from '../contexts/ThemeContext';

interface CardProps extends TouchableOpacityProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'default' | 'elevated' | 'outlined' | 'glass';
  padding?: keyof typeof SPACING;
  margin?: keyof typeof SPACING;
  borderRadius?: keyof typeof BORDER_RADIUS;
  onPress?: () => void;
  disabled?: boolean;
}

export const Card: React.FC<CardProps> = ({
  children,
  style,
  variant = 'default',
  padding = 'md',
  margin,
  borderRadius = 'md',
  onPress,
  disabled = false,
  ...props
}) => {
  const styles = useThemedStyles(createStyles);

  const cardStyle = [
    styles.base,
    styles[variant],
    {
      padding: SPACING[padding],
      borderRadius: BORDER_RADIUS[borderRadius],
    },
    margin && { margin: SPACING[margin] },
    style,
  ];

  if (onPress && !disabled) {
    return (
      <TouchableOpacity
        style={cardStyle}
        onPress={onPress}
        activeOpacity={0.8}
        disabled={disabled}
        {...props}
      >
        <RTLView style={styles.content}>
          {children}
        </RTLView>
      </TouchableOpacity>
    );
  }

  return (
    <RTLView style={cardStyle}>
      <RTLView style={styles.content}>
        {children}
      </RTLView>
    </RTLView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    base: {
      backgroundColor: colors.surface,
      overflow: 'hidden',
    },
    content: {
      flex: 1,
    },
    default: {
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.borderLight,
    },
    elevated: {
      backgroundColor: colors.surface,
      // Use boxShadow instead of ...SHADOWS.medium
      elevation: 3,
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.8,
      shadowRadius: 2,
    },
    outlined: {
      backgroundColor: 'transparent',
      borderWidth: 1,
      borderColor: colors.border,
    },
    glass: {
      backgroundColor: colors.glassBackground,
      borderWidth: 1,
      borderColor: colors.glassBorder,
      backdropFilter: 'blur(10px)',
    },
  });
