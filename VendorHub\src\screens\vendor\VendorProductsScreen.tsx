import React, { useState } from 'react';
import {
  StyleSheet,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  RefreshControl,
  Alert,
} from 'react-native';
import { useThemedStyles, useAuth, useProducts, useDebounce, useI18n } from '../../hooks';
import { RTLView, RTLText, RTLIcon } from '../../components/RTL';
import { Card, Button, Input, EmptyState, StatusBadge, SwipeableRow, SwipeActions, OptimizedFlatList, useOptimizedSearch, usePaginatedData, FloatingActionButton } from '../../components';
import {
  SPACING,
  FONT_SIZES,
  FONT_WEIGHTS,
  BORDER_RADIUS,
  ICON_SIZES,
} from '../../constants/theme';
import { formatCurrency } from '../../utils';
import type { ThemeColors } from '../../contexts/ThemeContext';
import type { Product } from '../../contexts/DataContext';

interface VendorProductsScreenProps {
  navigation: any;
}

export const VendorProductsScreen: React.FC<VendorProductsScreenProps> = ({ navigation }) => {
  const styles = useThemedStyles(createStyles);
  const { user } = useAuth();
  const { getProductsByVendor, deleteProduct } = useProducts();
  const { t } = useI18n();
  const [refreshing, setRefreshing] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'active' | 'inactive' | 'low_stock'>('all');

  // Get vendor's products
  const vendorProducts = user ? getProductsByVendor(user.id) : [];

  // Optimized search with debouncing
  const { searchQuery, setSearchQuery, filteredData: searchFilteredProducts } = useOptimizedSearch(
    vendorProducts,
    ['name', 'description', 'category'],
    300
  );

  // Apply additional filters to search results
  const filteredProducts = React.useMemo(() => {
    let result = searchFilteredProducts;

    // Apply status filter
    switch (selectedFilter) {
      case 'active':
        result = result.filter(product => product.isActive);
        break;
      case 'inactive':
        result = result.filter(product => !product.isActive);
        break;
      case 'low_stock':
        result = result.filter(product => product.inventory <= 10 && product.inventory > 0);
        break;
      default:
        break;
    }

    return result;
  }, [searchFilteredProducts, selectedFilter]);

  // Pagination for large product lists
  const { data: paginatedProducts, hasMore, loadMore } = usePaginatedData(filteredProducts, 20);

  const onRefresh = React.useCallback(async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => setRefreshing(false), 1000);
  }, []);

  const handleAddProduct = () => {
    navigation.navigate('AddProduct');
  };

  const handleEditProduct = (productId: string) => {
    navigation.navigate('EditProduct', { productId });
  };

  const handleDeleteProduct = (product: Product) => {
    Alert.alert(
      'Delete Product',
      `Are you sure you want to delete "${product.name}"? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            deleteProduct(product.id);
            Alert.alert('Success', 'Product deleted successfully');
          },
        },
      ]
    );
  };

  const handleToggleStatus = (product: Product) => {
    // This would typically call updateProduct
    Alert.alert(
      product.isActive ? 'Deactivate Product' : 'Activate Product',
      `Are you sure you want to ${product.isActive ? 'deactivate' : 'activate'} "${product.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: product.isActive ? 'Deactivate' : 'Activate',
          onPress: () => {
            // updateProduct(product.id, { isActive: !product.isActive });
            Alert.alert('Success', `Product ${product.isActive ? 'deactivated' : 'activated'} successfully`);
          },
        },
      ]
    );
  };

  const getStockStatus = (product: Product) => {
    if (product.inventory === 0) return { status: 'Out of Stock', variant: 'error' as const };
    if (product.inventory <= 10) return { status: 'Low Stock', variant: 'warning' as const };
    return { status: 'In Stock', variant: 'success' as const };
  };

  const renderProductItem = ({ item }: { item: Product }) => {
    const stockStatus = getStockStatus(item);

    // Define swipe actions for products
    const swipeActions = {
      leftActions: [
        SwipeActions.edit(() => handleEditProduct(item.id)),
      ],
      rightActions: [
        SwipeActions.delete(() => handleDeleteProduct(item)),
      ],
    };

    return (
      <SwipeableRow
        leftActions={swipeActions.leftActions}
        rightActions={swipeActions.rightActions}
        style={styles.swipeableContainer}
      >
        <Card style={styles.productCard} variant="elevated">
          <TouchableOpacity
            style={styles.productContent}
            onPress={() => handleEditProduct(item.id)}
          >
            <RTLView style={styles.productHeader}>
              <RTLView style={styles.productImage}>
                <RTLIcon name="image-outline" size={32} color="#CCCCCC" />
              </RTLView>
              <RTLView style={styles.productInfo}>
                <RTLText style={styles.productName} numberOfLines={2}>
                  {item.name}
                </RTLText>
                <RTLText style={styles.productCategory}>{item.category}</RTLText>
                <RTLView style={styles.productPricing}>
                  <RTLText style={styles.productPrice}>
                    {formatCurrency(item.price)}
                  </RTLText>
                  {item.originalPrice && item.originalPrice > item.price && (
                    <RTLText style={styles.originalPrice}>
                      {formatCurrency(item.originalPrice)}
                    </RTLText>
                  )}
                </RTLView>
              </RTLView>
              <RTLView style={styles.productStatus}>
                <StatusBadge
                  status={item.isActive ? 'Active' : 'Inactive'}
                  variant={item.isActive ? 'success' : 'error'}
                  style={styles.statusBadge}
                />
              </RTLView>
            </RTLView>

            <RTLView style={styles.productDetails}>
              <RTLView style={styles.productStats}>
                <RTLView style={styles.statItem}>
                  <RTLIcon name="cube-outline" size={16} color="#667eea" />
                  <RTLText style={styles.statText}>{item.inventory} in stock</RTLText>
                </RTLView>
                <RTLView style={styles.statItem}>
                  <RTLIcon name="star-outline" size={16} color="#FFD700" />
                  <RTLText style={styles.statText}>{item.rating.toFixed(1)} ({item.reviewCount})</RTLText>
                </RTLView>
              </RTLView>

              <StatusBadge
                status={stockStatus.status}
                variant={stockStatus.variant}
                style={styles.stockBadge}
              />
            </RTLView>

            {/* Swipe hint */}
            <RTLText style={styles.swipeHint}>
              ← Swipe to edit or delete →
            </RTLText>

            <RTLView style={styles.productActions}>
              <Button
                title="Edit"
                onPress={() => handleEditProduct(item.id)}
                variant="outline"
                size="small"
                style={styles.actionButton}
                leftIcon={<RTLIcon name="pencil-outline" size={16} color="#667eea" />}
              />
              <Button
                title={item.isActive ? "Deactivate" : "Activate"}
                onPress={() => handleToggleStatus(item)}
                variant="outline"
                size="small"
                style={styles.actionButton}
                leftIcon={
                  <RTLIcon
                    name={item.isActive ? "pause-outline" : "play-outline"}
                    size={16}
                    color="#667eea"
                  />
                }
              />
              <Button
                title="Delete"
                onPress={() => handleDeleteProduct(item)}
                variant="outline"
                size="small"
                style={[styles.actionButton, styles.deleteButton]}
                leftIcon={<RTLIcon name="trash-outline" size={16} color="#FF6B6B" />}
              />
            </RTLView>
          </TouchableOpacity>
        </Card>
      </SwipeableRow>
    );
  };

  const filterOptions = [
    { label: 'All', value: 'all' as const },
    { label: 'Active', value: 'active' as const },
    { label: 'Inactive', value: 'inactive' as const },
    { label: 'Low Stock', value: 'low_stock' as const },
  ];

  const renderHeader = () => (
    <RTLView style={styles.header}>
      {/* Search */}
      <Input
        placeholder="Search products..."
        value={searchQuery}
        onChangeText={setSearchQuery}
        leftIcon={<RTLIcon name="search-outline" size={20} color="#CCCCCC" />}
        style={styles.searchInput}
      />

      {/* Filter Chips */}
      <RTLView style={styles.filterContainer}>
        {filterOptions.map((option) => (
          <TouchableOpacity
            key={option.value}
            style={[
              styles.filterChip,
              selectedFilter === option.value && styles.filterChipActive,
            ]}
            onPress={() => setSelectedFilter(option.value)}
          >
            <RTLText
              style={[
                styles.filterChipText,
                selectedFilter === option.value && styles.filterChipTextActive,
              ]}
            >
              {option.label}
            </RTLText>
          </TouchableOpacity>
        ))}
      </RTLView>

      {/* Stats */}
      <RTLView style={styles.statsContainer}>
        <RTLText style={styles.resultsText}>
          {filteredProducts.length} product{filteredProducts.length !== 1 ? 's' : ''} found
        </RTLText>
      </RTLView>
    </RTLView>
  );

  return (
    <SafeAreaView style={styles.container}>
      <OptimizedFlatList
        data={paginatedProducts}
        renderItem={renderProductItem}
        keyExtractor={(item) => item.id}
        estimatedItemSize={180}
        enableVirtualization={true}
        enableMemoryOptimization={true}
        enableScrollOptimization={true}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={
          <EmptyState
            icon="cube-outline"
            title="No products found"
            description={
              searchQuery || selectedFilter !== 'all'
                ? "Try adjusting your search or filters"
                : "Start by adding your first product"
            }
            actionButton={
              !searchQuery && selectedFilter === 'all' ? (
                <Button
                  title="Add Product"
                  onPress={handleAddProduct}
                  leftIcon={<Ionicons name="add-outline" size={20} color="#FFFFFF" />}
                />
              ) : undefined
            }
          />
        }
        onEndReached={hasMore ? loadMore : undefined}
        onEndReachedThreshold={0.1}
        contentContainerStyle={styles.listContent}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      />

      {/* Floating Action Button */}
      <FloatingActionButton
        icon="add"
        onPress={handleAddProduct}
        size="medium"
        backgroundColor="#667eea"
        position="bottom-right"
      />
    </SafeAreaView>
  );
};

const createStyles = (colors: ThemeColors) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listContent: {
      padding: SPACING.md,
      paddingBottom: 80, // Space for FAB
    },
    header: {
      marginBottom: SPACING.lg,
    },
    searchInput: {
      marginBottom: SPACING.md,
    },
    filterContainer: {
      flexDirection: 'row',
      marginBottom: SPACING.md,
    },
    filterChip: {
      paddingHorizontal: SPACING.md,
      paddingVertical: SPACING.sm,
      borderRadius: BORDER_RADIUS.full,
      backgroundColor: colors.surface,
      marginRight: SPACING.sm,
      borderWidth: 1,
      borderColor: colors.border,
    },
    filterChipActive: {
      backgroundColor: '#667eea',
      borderColor: '#667eea',
    },
    filterChipText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      fontWeight: FONT_WEIGHTS.medium,
    },
    filterChipTextActive: {
      color: '#FFFFFF',
    },
    statsContainer: {
      marginBottom: SPACING.sm,
    },
    resultsText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
    },
    productCard: {
      marginBottom: SPACING.md,
    },
    productContent: {
      // No additional styles needed, Card handles the touch
    },
    productHeader: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: SPACING.sm,
    },
    productImage: {
      width: 60,
      height: 60,
      backgroundColor: colors.surface,
      borderRadius: BORDER_RADIUS.sm,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: SPACING.sm,
    },
    productInfo: {
      flex: 1,
      marginRight: SPACING.sm,
    },
    productName: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.semibold,
      color: colors.textPrimary,
      marginBottom: SPACING.xs,
    },
    productCategory: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginBottom: SPACING.xs,
    },
    productPricing: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    productPrice: {
      fontSize: FONT_SIZES.md,
      fontWeight: FONT_WEIGHTS.bold,
      color: '#667eea',
      marginRight: SPACING.sm,
    },
    originalPrice: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      textDecorationLine: 'line-through',
    },
    productStatus: {
      alignItems: 'flex-end',
    },
    statusBadge: {
      marginBottom: SPACING.xs,
    },
    productDetails: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: SPACING.md,
      paddingTop: SPACING.sm,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    productStats: {
      flexDirection: 'row',
      flex: 1,
    },
    statItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginRight: SPACING.md,
    },
    statText: {
      fontSize: FONT_SIZES.sm,
      color: colors.textSecondary,
      marginLeft: SPACING.xs,
    },
    stockBadge: {
      // Positioned on the right
    },
    swipeHint: {
      fontSize: FONT_SIZES.xs,
      color: colors.primary,
      textAlign: 'center',
      fontStyle: 'italic',
      opacity: 0.7,
      marginVertical: SPACING.xs,
    },
    productActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      gap: SPACING.sm,
    },
    actionButton: {
      flex: 1,
    },
    deleteButton: {
      borderColor: '#FF6B6B',
    },
    swipeableContainer: {
      marginBottom: SPACING.md,
    },
  });
