import React, { useMemo } from 'react';
import { SafeAreaView, SafeAreaViewProps, StyleSheet, ViewStyle } from 'react-native';
import { useI18n } from '../../hooks/useI18n';

interface RTLSafeAreaViewProps extends SafeAreaViewProps {
  style?: ViewStyle | ViewStyle[];
  children?: React.ReactNode;
  disableRTL?: boolean; // Option to disable RTL transformation for specific cases
}

export const RTLSafeAreaView: React.FC<RTLSafeAreaViewProps> = ({
  style,
  children,
  disableRTL = false,
  ...props
}) => {
  const { isRTL } = useI18n();

  const rtlStyle = useMemo(() => {
    if (!style || !isRTL || disableRTL) return style;
    
    // Create RTL version of the style
    const flattenedStyle = StyleSheet.flatten(style);
    const rtlFlattenedStyle = { ...flattenedStyle };
    
    // Flip flexDirection
    if (flattenedStyle.flexDirection === 'row') {
      rtlFlattenedStyle.flexDirection = 'row-reverse';
    } else if (flattenedStyle.flexDirection === 'row-reverse') {
      rtlFlattenedStyle.flexDirection = 'row';
    }
    
    // Flip text alignment
    if (flattenedStyle.textAlign === 'left') {
      rtlFlattenedStyle.textAlign = 'right';
    } else if (flattenedStyle.textAlign === 'right') {
      rtlFlattenedStyle.textAlign = 'left';
    }
    
    // Flip padding
    if (flattenedStyle.paddingLeft !== undefined) {
      rtlFlattenedStyle.paddingRight = flattenedStyle.paddingLeft;
      delete rtlFlattenedStyle.paddingLeft;
    }
    if (flattenedStyle.paddingRight !== undefined) {
      rtlFlattenedStyle.paddingLeft = flattenedStyle.paddingRight;
      delete rtlFlattenedStyle.paddingRight;
    }
    if (flattenedStyle.paddingStart !== undefined) {
      rtlFlattenedStyle.paddingEnd = flattenedStyle.paddingStart;
      delete rtlFlattenedStyle.paddingStart;
    }
    if (flattenedStyle.paddingEnd !== undefined) {
      rtlFlattenedStyle.paddingStart = flattenedStyle.paddingEnd;
      delete rtlFlattenedStyle.paddingEnd;
    }
    
    // Flip margin
    if (flattenedStyle.marginLeft !== undefined) {
      rtlFlattenedStyle.marginRight = flattenedStyle.marginLeft;
      delete rtlFlattenedStyle.marginLeft;
    }
    if (flattenedStyle.marginRight !== undefined) {
      rtlFlattenedStyle.marginLeft = flattenedStyle.marginRight;
      delete rtlFlattenedStyle.marginRight;
    }
    if (flattenedStyle.marginStart !== undefined) {
      rtlFlattenedStyle.marginEnd = flattenedStyle.marginStart;
      delete rtlFlattenedStyle.marginStart;
    }
    if (flattenedStyle.marginEnd !== undefined) {
      rtlFlattenedStyle.marginStart = flattenedStyle.marginEnd;
      delete rtlFlattenedStyle.marginEnd;
    }
    
    // Flip positioning
    if (flattenedStyle.left !== undefined) {
      rtlFlattenedStyle.right = flattenedStyle.left;
      delete rtlFlattenedStyle.left;
    }
    if (flattenedStyle.right !== undefined) {
      rtlFlattenedStyle.left = flattenedStyle.right;
      delete rtlFlattenedStyle.right;
    }
    
    // Flip border radius for asymmetric corners
    if (flattenedStyle.borderTopLeftRadius !== undefined) {
      rtlFlattenedStyle.borderTopRightRadius = flattenedStyle.borderTopLeftRadius;
      delete rtlFlattenedStyle.borderTopLeftRadius;
    }
    if (flattenedStyle.borderTopRightRadius !== undefined) {
      rtlFlattenedStyle.borderTopLeftRadius = flattenedStyle.borderTopRightRadius;
      delete rtlFlattenedStyle.borderTopRightRadius;
    }
    if (flattenedStyle.borderBottomLeftRadius !== undefined) {
      rtlFlattenedStyle.borderBottomRightRadius = flattenedStyle.borderBottomLeftRadius;
      delete rtlFlattenedStyle.borderBottomLeftRadius;
    }
    if (flattenedStyle.borderBottomRightRadius !== undefined) {
      rtlFlattenedStyle.borderBottomLeftRadius = flattenedStyle.borderBottomRightRadius;
      delete rtlFlattenedStyle.borderBottomRightRadius;
    }

    return rtlFlattenedStyle;
  }, [style, isRTL, disableRTL]);

  return <SafeAreaView style={rtlStyle} {...props}>{children}</SafeAreaView>;
};
